"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, FileText, Eye, Edit3, Download, Trash2, <PERSON><PERSON>, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DEMO_RESUMES } from "@/lib/demo-auth"

const documentTypes = [
  { id: "all", name: "All Documents", count: 5 },
  { id: "resumes", name: "Resumes", count: 3 },
  { id: "cover-letters", name: "Cover Letters", count: 1 },
  { id: "websites", name: "Websites", count: 1 }
]

const mockDocuments = [
  ...DEMO_RESUMES,
  {
    id: "cover-letter-1",
    title: "Software Engineer Cover Letter",
    template: "Professional",
    updatedAt: "2025-01-06",
    status: "complete",
    views: 15,
    downloads: 3,
    type: "cover-letter"
  },
  {
    id: "website-1", 
    title: "Personal Portfolio Website",
    template: "Modern",
    updatedAt: "2025-01-04",
    status: "draft",
    views: 8,
    downloads: 0,
    type: "website"
  }
]

export default function DocumentsPage() {
  const [selectedType, setSelectedType] = useState("all")
  const [documents, setDocuments] = useState(mockDocuments)

  const filteredDocuments = selectedType === "all" 
    ? documents 
    : documents.filter(doc => {
        if (selectedType === "resumes") return !doc.type || doc.type === "resume"
        return doc.type === selectedType
      })

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this document?')) {
      setDocuments(documents.filter(doc => doc.id !== id))
      alert('Document deleted successfully! (Demo mode)')
    }
  }

  const handleDuplicate = (document: any) => {
    const newDocument = {
      ...document,
      id: `${document.id}-copy`,
      title: `${document.title} (Copy)`,
      updatedAt: new Date().toISOString().split('T')[0]
    }
    setDocuments([newDocument, ...documents])
    alert('Document duplicated successfully! (Demo mode)')
  }

  const getDocumentIcon = (type?: string) => {
    switch (type) {
      case "cover-letter": return "📄"
      case "website": return "🌐"
      default: return "📋"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Documents</h1>
          <p className="text-gray-600 mt-2">
            Manage all your resumes, cover letters, and websites
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Link href="/dashboard/resumes/new">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Create New
            </Button>
          </Link>
        </div>
      </div>

      {/* Document Type Filter */}
      <div className="flex space-x-2 overflow-x-auto">
        {documentTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => setSelectedType(type.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
              selectedType === type.id
                ? "bg-blue-600 text-white" 
                : "bg-gray-100 text-gray-600 hover:bg-gray-200"
            }`}
          >
            {type.name} ({type.count})
          </button>
        ))}
      </div>

      {/* Documents Grid */}
      {filteredDocuments.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
          <p className="text-gray-600 mb-6">Create your first document to get started</p>
          <Link href="/dashboard/resumes/new">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Document
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDocuments.map((document) => (
            <Card key={document.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-lg">{getDocumentIcon(document.type)}</span>
                      <CardTitle className="text-lg line-clamp-2">{document.title}</CardTitle>
                    </div>
                    <CardDescription className="mt-1">{document.template}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-1 ml-2">
                    <button 
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => handleDuplicate(document)}
                      title="Duplicate"
                    >
                      <Copy className="h-4 w-4 text-gray-400" />
                    </button>
                    <button 
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => handleDelete(document.id)}
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4 text-gray-400" />
                    </button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {/* Status and Date */}
                  <div className="flex items-center justify-between text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      document.status === 'complete' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {document.status === 'complete' ? 'Complete' : 'Draft'}
                    </span>
                    <span className="text-gray-600">Updated {document.updatedAt}</span>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{document.views}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Download className="h-4 w-4" />
                        <span>{document.downloads}</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" className="flex-1">
                      <Edit3 className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="flex-1"
                      onClick={() => alert('Download coming soon! (Demo mode)')}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => alert('Preview coming soon! (Demo mode)')}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
        <Card className="border-2 border-dashed border-blue-200 hover:border-blue-400 transition-colors cursor-pointer">
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Create Resume</h3>
            <p className="text-sm text-gray-600 mb-4">Build a professional resume</p>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Get Started
            </Button>
          </CardContent>
        </Card>

        <Card className="border-2 border-dashed border-purple-200 hover:border-purple-400 transition-colors cursor-pointer">
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
              <FileText className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Create Cover Letter</h3>
            <p className="text-sm text-gray-600 mb-4">Write a compelling cover letter</p>
            <Button variant="outline" onClick={() => alert('Cover Letter Builder coming soon!')}>
              Get Started
            </Button>
          </CardContent>
        </Card>

        <Card className="border-2 border-dashed border-green-200 hover:border-green-400 transition-colors cursor-pointer">
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <FileText className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Create Website</h3>
            <p className="text-sm text-gray-600 mb-4">Turn your resume into a website</p>
            <Button variant="outline" onClick={() => alert('Website Builder coming soon!')}>
              Get Started
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
