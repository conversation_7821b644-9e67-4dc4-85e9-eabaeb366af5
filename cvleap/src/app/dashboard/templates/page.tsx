"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, Star, Download, Eye, Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const templates = [
  {
    id: "modern-professional",
    name: "Modern Professional",
    description: "Clean, contemporary design perfect for corporate roles",
    category: "Professional",
    isPremium: false,
    rating: 4.8,
    downloads: 12500,
    colors: ["#2563eb", "#1e40af", "#3b82f6"]
  },
  {
    id: "creative-bold",
    name: "Creative Bold",
    description: "Eye-catching design for creative professionals",
    category: "Creative",
    isPremium: true,
    rating: 4.9,
    downloads: 8900,
    colors: ["#7c3aed", "#6d28d9", "#8b5cf6"]
  },
  {
    id: "minimal-clean",
    name: "Minimal Clean",
    description: "Simple, elegant layout that focuses on content",
    category: "Simple",
    isPremium: false,
    rating: 4.7,
    downloads: 15200,
    colors: ["#374151", "#4b5563", "#6b7280"]
  },
  {
    id: "executive-elite",
    name: "Executive Elite",
    description: "Sophisticated design for senior-level positions",
    category: "Professional",
    isPremium: true,
    rating: 4.9,
    downloads: 6700,
    colors: ["#1f2937", "#374151", "#4b5563"]
  },
  {
    id: "tech-innovator",
    name: "Tech Innovator",
    description: "Modern tech-focused design with clean lines",
    category: "Modern",
    isPremium: false,
    rating: 4.8,
    downloads: 11300,
    colors: ["#059669", "#047857", "#10b981"]
  },
  {
    id: "artistic-flair",
    name: "Artistic Flair",
    description: "Creative template with artistic elements",
    category: "Creative",
    isPremium: true,
    rating: 4.6,
    downloads: 5400,
    colors: ["#dc2626", "#b91c1c", "#ef4444"]
  }
]

const categories = ["All", "Professional", "Creative", "Simple", "Modern"]

export default function DashboardTemplatesPage() {
  const [selectedCategory, setSelectedCategory] = useState("All")

  const filteredTemplates = selectedCategory === "All" 
    ? templates 
    : templates.filter(template => template.category === selectedCategory)

  const handleUseTemplate = (templateId: string) => {
    // In a real app, this would navigate to the resume builder with the selected template
    alert(`Starting new resume with template: ${templateId} (Demo mode)`)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Resume Templates</h1>
        <p className="text-gray-600 mt-2">
          Choose from our collection of professionally designed templates
        </p>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category
                ? "bg-blue-600 text-white" 
                : "bg-gray-100 text-gray-600 hover:bg-gray-200"
            }`}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
            <div className="relative">
              {/* Template Preview */}
              <div className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <div className="absolute inset-4 bg-white rounded shadow-sm">
                  {/* Mock resume content */}
                  <div className="p-3 space-y-2">
                    <div className="h-3 bg-gray-800 rounded w-3/4"></div>
                    <div className="h-2 bg-gray-400 rounded w-1/2"></div>
                    <div className="space-y-1 mt-4">
                      <div className="h-1.5 bg-gray-300 rounded w-full"></div>
                      <div className="h-1.5 bg-gray-300 rounded w-5/6"></div>
                      <div className="h-1.5 bg-gray-300 rounded w-4/6"></div>
                    </div>
                    <div className="mt-4 space-y-1">
                      <div className="h-2 bg-gray-600 rounded w-2/3"></div>
                      <div className="h-1 bg-gray-300 rounded w-full"></div>
                      <div className="h-1 bg-gray-300 rounded w-3/4"></div>
                    </div>
                  </div>
                  
                  {/* Color accent */}
                  <div 
                    className="absolute top-0 left-0 w-1 h-full"
                    style={{ backgroundColor: template.colors[0] }}
                  ></div>
                </div>
              </div>

              {/* Premium Badge */}
              {template.isPremium && (
                <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <Crown className="h-3 w-3 mr-1" />
                  Pro
                </div>
              )}

              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 space-y-2">
                  <Button 
                    className="bg-white text-gray-900 hover:bg-gray-100"
                    onClick={() => alert('Preview coming soon! (Demo mode)')}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                </div>
              </div>
            </div>

            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{template.name}</CardTitle>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="text-sm text-gray-600">{template.rating}</span>
                </div>
              </div>
              <CardDescription>{template.description}</CardDescription>
            </CardHeader>

            <CardContent className="pt-0">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                <span className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                  {template.category}
                </span>
                <div className="flex items-center space-x-1">
                  <Download className="h-3 w-3" />
                  <span>{template.downloads.toLocaleString()}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button 
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                  onClick={() => handleUseTemplate(template.id)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Use Template
                </Button>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={() => alert('Preview coming soon! (Demo mode)')}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
          <p className="text-gray-600 mb-4">Try selecting a different category</p>
          <Button onClick={() => setSelectedCategory("All")}>
            Show All Templates
          </Button>
        </div>
      )}

      {/* Premium Upgrade CTA */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Unlock Premium Templates
              </h3>
              <p className="text-gray-600">
                Get access to exclusive premium templates and advanced features
              </p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Crown className="h-4 w-4 mr-2" />
              Upgrade to Pro
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
