"use client"

import { useState } from "react"
import { Upload, BarChart3, CheckCircle, AlertTriangle, XCircle, FileText, Download, Zap } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const analysisResults = {
  overallScore: 78,
  sections: [
    {
      name: "ATS Compatibility",
      score: 85,
      status: "good",
      issues: [
        "✓ Standard fonts used",
        "✓ Simple formatting detected",
        "⚠ Consider adding more keywords"
      ]
    },
    {
      name: "Content Quality",
      score: 72,
      status: "warning", 
      issues: [
        "✓ Good use of action verbs",
        "⚠ Missing quantified achievements",
        "⚠ Some sections could be more detailed"
      ]
    },
    {
      name: "Structure & Format",
      score: 90,
      status: "good",
      issues: [
        "✓ Clear section headers",
        "✓ Consistent formatting",
        "✓ Appropriate length"
      ]
    },
    {
      name: "Keywords & Skills",
      score: 65,
      status: "warning",
      issues: [
        "⚠ Missing industry keywords",
        "⚠ Skills section needs expansion",
        "✓ Good technical skills coverage"
      ]
    }
  ]
}

const recommendations = [
  {
    priority: "high",
    title: "Add Quantified Achievements",
    description: "Include specific numbers, percentages, and metrics to demonstrate your impact.",
    example: "Instead of 'Improved sales' write 'Increased sales by 25% over 6 months'"
  },
  {
    priority: "medium", 
    title: "Include More Industry Keywords",
    description: "Add relevant keywords from the job description to improve ATS compatibility.",
    example: "For a software role, include: 'Agile', 'CI/CD', 'Cloud Computing'"
  },
  {
    priority: "medium",
    title: "Expand Skills Section", 
    description: "Add both technical and soft skills relevant to your target role.",
    example: "Include programming languages, tools, and leadership skills"
  },
  {
    priority: "low",
    title: "Optimize Section Order",
    description: "Place your strongest sections (experience, skills) near the top.",
    example: "Consider moving 'Projects' section above 'Education' if more relevant"
  }
]

export default function AnalysisPage() {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [hasResults, setHasResults] = useState(true) // Set to true for demo

  const handleAnalyze = () => {
    setIsAnalyzing(true)
    setTimeout(() => {
      setIsAnalyzing(false)
      setHasResults(true)
    }, 3000)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "good": return <CheckCircle className="h-5 w-5 text-green-600" />
      case "warning": return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case "error": return <XCircle className="h-5 w-5 text-red-600" />
      default: return <CheckCircle className="h-5 w-5 text-gray-400" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "low": return "bg-blue-100 text-blue-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Resume Analysis</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Get detailed feedback on your resume's ATS compatibility, content quality, 
          and optimization suggestions
        </p>
      </div>

      {!hasResults ? (
        /* Upload Section */
        <Card className="border-2 border-dashed border-blue-200">
          <CardContent className="p-12 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Upload className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Upload Your Resume
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Upload your resume in PDF or Word format to get a comprehensive analysis
            </p>
            <div className="space-y-4">
              <Button 
                className="bg-blue-600 hover:bg-blue-700"
                onClick={handleAnalyze}
                disabled={isAnalyzing}
              >
                {isAnalyzing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </>
                )}
              </Button>
              <p className="text-sm text-gray-500">
                Supported formats: PDF, DOC, DOCX (Max 5MB)
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Results Section */
        <>
          {/* Overall Score */}
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="p-8 text-center">
              <div className="flex items-center justify-center space-x-4 mb-4">
                <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg">
                  <span className={`text-3xl font-bold ${getScoreColor(analysisResults.overallScore)}`}>
                    {analysisResults.overallScore}
                  </span>
                </div>
                <div className="text-left">
                  <h3 className="text-2xl font-bold text-gray-900">Overall Score</h3>
                  <p className="text-gray-600">Your resume is performing well!</p>
                </div>
              </div>
              <div className="flex justify-center space-x-4">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Download className="h-4 w-4 mr-2" />
                  Download Report
                </Button>
                <Button variant="outline">
                  <Zap className="h-4 w-4 mr-2" />
                  Auto-Optimize
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Section Scores */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Detailed Analysis</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {analysisResults.sections.map((section) => (
                <Card key={section.name}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center">
                        {getStatusIcon(section.status)}
                        <span className="ml-2">{section.name}</span>
                      </CardTitle>
                      <span className={`text-2xl font-bold ${getScoreColor(section.score)}`}>
                        {section.score}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {section.issues.map((issue, index) => (
                        <div key={index} className="text-sm">
                          {issue.startsWith("✓") ? (
                            <span className="text-green-600">{issue}</span>
                          ) : (
                            <span className="text-yellow-600">{issue}</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Recommendations */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Recommendations</h2>
            <div className="space-y-4">
              {recommendations.map((rec, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(rec.priority)}`}>
                            {rec.priority.toUpperCase()}
                          </span>
                          <h3 className="font-semibold text-gray-900">{rec.title}</h3>
                        </div>
                        <p className="text-gray-600 mb-3">{rec.description}</p>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm text-gray-700">
                            <strong>Example:</strong> {rec.example}
                          </p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="ml-4">
                        Apply Fix
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* ATS Keywords */}
          <Card>
            <CardHeader>
              <CardTitle>ATS Keywords Analysis</CardTitle>
              <CardDescription>
                Keywords found in your resume vs. common job requirements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-green-600 mb-3">✓ Found Keywords</h4>
                  <div className="flex flex-wrap gap-2">
                    {["JavaScript", "React", "Node.js", "Git", "Agile", "Leadership"].map((keyword) => (
                      <span key={keyword} className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-red-600 mb-3">⚠ Missing Keywords</h4>
                  <div className="flex flex-wrap gap-2">
                    {["TypeScript", "AWS", "Docker", "CI/CD", "Microservices", "Testing"].map((keyword) => (
                      <span key={keyword} className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Items */}
          <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Next Steps</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                  <span>Add quantified achievements to your experience section</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                  <span>Include missing industry keywords in your skills section</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                  <span>Review and optimize your professional summary</span>
                </div>
              </div>
              <Button className="mt-4 bg-green-600 hover:bg-green-700">
                <FileText className="h-4 w-4 mr-2" />
                Edit Resume
              </Button>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
