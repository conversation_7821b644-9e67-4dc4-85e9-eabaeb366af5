"use client"

import { useState } from "react"
import { Eye, Download, Star, Users, Briefcase, Code, Palette, TrendingUp, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const categories = [
  { id: "all", name: "All Examples", count: 47, icon: Users },
  { id: "engineering", name: "Engineering", count: 15, icon: Code },
  { id: "business", name: "Business", count: 12, icon: Briefcase },
  { id: "design", name: "Design", count: 8, icon: Palette },
  { id: "marketing", name: "Marketing", count: 7, icon: TrendingUp },
  { id: "sales", name: "Sales", count: 5, icon: Users }
]

const resumeExamples = [
  {
    id: 1,
    title: "Senior Software Engineer",
    company: "Google",
    industry: "Technology",
    category: "engineering",
    experience: "5+ years",
    salary: "$180,000",
    location: "San Francisco, CA",
    rating: 4.9,
    downloads: 2847,
    description: "Led development of microservices architecture serving 10M+ users",
    highlights: [
      "Increased system performance by 40%",
      "Led team of 8 engineers",
      "Implemented CI/CD pipeline reducing deployment time by 60%"
    ],
    skills: ["Python", "AWS", "Kubernetes", "React", "Leadership"],
    template: "Modern Professional"
  },
  {
    id: 2,
    title: "Product Manager",
    company: "Meta",
    industry: "Technology", 
    category: "business",
    experience: "3-5 years",
    salary: "$150,000",
    location: "Menlo Park, CA",
    rating: 4.8,
    downloads: 1923,
    description: "Drove product strategy for social media features with 500M+ users",
    highlights: [
      "Launched 3 major features increasing user engagement by 25%",
      "Managed cross-functional team of 12",
      "Reduced customer churn by 15% through data-driven insights"
    ],
    skills: ["Product Strategy", "Analytics", "A/B Testing", "SQL", "Leadership"],
    template: "Executive Elite"
  },
  {
    id: 3,
    title: "UX Designer",
    company: "Apple",
    industry: "Technology",
    category: "design", 
    experience: "2-4 years",
    salary: "$120,000",
    location: "Cupertino, CA",
    rating: 4.7,
    downloads: 1654,
    description: "Designed user experiences for iOS apps used by millions",
    highlights: [
      "Improved app usability scores by 30%",
      "Led design for 2 award-winning mobile apps",
      "Conducted user research with 500+ participants"
    ],
    skills: ["Figma", "Sketch", "User Research", "Prototyping", "Design Systems"],
    template: "Creative Bold"
  },
  {
    id: 4,
    title: "Marketing Manager",
    company: "Spotify",
    industry: "Technology",
    category: "marketing",
    experience: "3-5 years", 
    salary: "$95,000",
    location: "New York, NY",
    rating: 4.6,
    downloads: 1432,
    description: "Drove growth marketing initiatives for music streaming platform",
    highlights: [
      "Increased user acquisition by 45% through digital campaigns",
      "Managed $2M annual marketing budget",
      "Improved conversion rates by 20% through A/B testing"
    ],
    skills: ["Digital Marketing", "Analytics", "SEO", "Content Strategy", "A/B Testing"],
    template: "Modern Professional"
  },
  {
    id: 5,
    title: "Sales Director",
    company: "Salesforce",
    industry: "Technology",
    category: "sales",
    experience: "7+ years",
    salary: "$200,000",
    location: "San Francisco, CA", 
    rating: 4.8,
    downloads: 1287,
    description: "Built and led enterprise sales team achieving 150% of quota",
    highlights: [
      "Generated $50M+ in annual revenue",
      "Built sales team from 5 to 25 members",
      "Achieved 150% of quota for 3 consecutive years"
    ],
    skills: ["Enterprise Sales", "Team Leadership", "CRM", "Negotiation", "Strategy"],
    template: "Executive Elite"
  }
]

export default function ExamplesPage() {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedExperience, setSelectedExperience] = useState("all")

  const filteredExamples = resumeExamples.filter(example => {
    const categoryMatch = selectedCategory === "all" || example.category === selectedCategory
    const experienceMatch = selectedExperience === "all" || example.experience === selectedExperience
    return categoryMatch && experienceMatch
  })

  const experienceLevels = [
    { id: "all", name: "All Levels" },
    { id: "0-2 years", name: "Entry Level (0-2 years)" },
    { id: "2-4 years", name: "Mid Level (2-4 years)" },
    { id: "3-5 years", name: "Senior Level (3-5 years)" },
    { id: "5+ years", name: "Expert Level (5+ years)" },
    { id: "7+ years", name: "Leadership (7+ years)" }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Resume Examples</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Browse real resumes from professionals who got hired at top companies. 
          Get inspired and learn what works.
        </p>
      </div>

      {/* Filters */}
      <div className="space-y-4">
        {/* Category Filter */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Filter by Industry</h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? "bg-blue-600 text-white" 
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                <category.icon className="h-4 w-4 mr-2" />
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>

        {/* Experience Filter */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Filter by Experience</h3>
          <div className="flex flex-wrap gap-2">
            {experienceLevels.map((level) => (
              <button
                key={level.id}
                onClick={() => setSelectedExperience(level.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedExperience === level.id
                    ? "bg-green-600 text-white" 
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {level.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {filteredExamples.length} resume examples
        </p>
        <Button variant="outline" size="sm">
          <Filter className="h-4 w-4 mr-2" />
          More Filters
        </Button>
      </div>

      {/* Resume Examples Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {filteredExamples.map((example) => (
          <Card key={example.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-xl mb-1">{example.title}</CardTitle>
                  <CardDescription className="text-base">
                    {example.company} • {example.location}
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-1 ml-4">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium">{example.rating}</span>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {/* Key Info */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Experience:</span>
                    <span className="font-medium ml-2">{example.experience}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Salary:</span>
                    <span className="font-medium ml-2">{example.salary}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Template:</span>
                    <span className="font-medium ml-2">{example.template}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Downloads:</span>
                    <span className="font-medium ml-2">{example.downloads.toLocaleString()}</span>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-700">{example.description}</p>

                {/* Key Highlights */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Key Achievements:</h4>
                  <ul className="space-y-1">
                    {example.highlights.map((highlight, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        {highlight}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Skills */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Top Skills:</h4>
                  <div className="flex flex-wrap gap-2">
                    {example.skills.map((skill) => (
                      <span 
                        key={skill}
                        className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-3 pt-4 border-t">
                  <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
                    <Eye className="h-4 w-4 mr-2" />
                    View Resume
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Success Stories */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
        <CardContent className="p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
            Success Stories
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">2,847</div>
              <p className="text-sm text-gray-600">People hired using these examples</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">94%</div>
              <p className="text-sm text-gray-600">Success rate for interview callbacks</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">$15K</div>
              <p className="text-sm text-gray-600">Average salary increase reported</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tips Section */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use These Examples</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Study the Structure</h4>
              <p className="text-sm text-gray-600">
                Notice how successful resumes organize information and prioritize content
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Adapt, Don't Copy</h4>
              <p className="text-sm text-gray-600">
                Use these as inspiration but customize content to reflect your unique experience
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Focus on Achievements</h4>
              <p className="text-sm text-gray-600">
                See how top performers quantify their impact with specific numbers and results
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Match Your Industry</h4>
              <p className="text-sm text-gray-600">
                Look for examples from your field to understand industry-specific expectations
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
