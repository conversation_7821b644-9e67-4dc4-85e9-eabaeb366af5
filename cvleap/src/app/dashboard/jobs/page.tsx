"use client"

import { useState } from "react"
import { Search, MapPin, DollarSign, Clock, Bookmark, ExternalLink, Filter, Briefcase, Users, TrendingUp } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"

const jobCategories = [
  { id: "all", name: "All Jobs", count: 1247 },
  { id: "engineering", name: "Engineering", count: 423 },
  { id: "product", name: "Product", count: 156 },
  { id: "design", name: "Design", count: 89 },
  { id: "marketing", name: "Marketing", count: 234 },
  { id: "sales", name: "Sales", count: 178 },
  { id: "data", name: "Data Science", count: 167 }
]

const featuredJobs = [
  {
    id: 1,
    title: "Senior Software Engineer",
    company: "Google",
    location: "Mountain View, CA",
    type: "Full-time",
    salary: "$150,000 - $200,000",
    posted: "2 days ago",
    description: "Join our team building the next generation of search technology...",
    skills: ["React", "TypeScript", "Node.js", "Python"],
    logo: "🔍",
    remote: true,
    featured: true
  },
  {
    id: 2,
    title: "Product Manager",
    company: "Meta",
    location: "Menlo Park, CA", 
    type: "Full-time",
    salary: "$140,000 - $180,000",
    posted: "1 day ago",
    description: "Lead product strategy for our social media platform...",
    skills: ["Strategy", "Analytics", "Leadership", "SQL"],
    logo: "📘",
    remote: false,
    featured: true
  },
  {
    id: 3,
    title: "UX Designer",
    company: "Apple",
    location: "Cupertino, CA",
    type: "Full-time", 
    salary: "$120,000 - $160,000",
    posted: "3 days ago",
    description: "Design intuitive user experiences for our mobile apps...",
    skills: ["Figma", "Sketch", "Prototyping", "User Research"],
    logo: "🍎",
    remote: true,
    featured: false
  },
  {
    id: 4,
    title: "Data Scientist",
    company: "Netflix",
    location: "Los Gatos, CA",
    type: "Full-time",
    salary: "$130,000 - $170,000", 
    posted: "1 week ago",
    description: "Analyze user behavior to improve content recommendations...",
    skills: ["Python", "SQL", "Machine Learning", "Statistics"],
    logo: "🎬",
    remote: true,
    featured: false
  },
  {
    id: 5,
    title: "Marketing Manager",
    company: "Spotify",
    location: "New York, NY",
    type: "Full-time",
    salary: "$90,000 - $120,000",
    posted: "5 days ago", 
    description: "Drive growth marketing initiatives for our music platform...",
    skills: ["Digital Marketing", "Analytics", "A/B Testing", "SEO"],
    logo: "🎵",
    remote: true,
    featured: false
  }
]

const jobStats = [
  { label: "Total Jobs", value: "1,247", icon: Briefcase, color: "text-blue-600" },
  { label: "Companies", value: "156", icon: Users, color: "text-green-600" },
  { label: "New This Week", value: "89", icon: TrendingUp, color: "text-purple-600" },
  { label: "Remote Jobs", value: "734", icon: MapPin, color: "text-orange-600" }
]

export default function JobsPage() {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [savedJobs, setSavedJobs] = useState<number[]>([])

  const filteredJobs = featuredJobs.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.company.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesSearch
  })

  const toggleSaveJob = (jobId: number) => {
    setSavedJobs(prev => 
      prev.includes(jobId) 
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Find Your Dream Job</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Discover opportunities from top companies that match your skills and career goals
        </p>
      </div>

      {/* Search Bar */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search jobs, companies, or keywords..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700">
                Search Jobs
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Job Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {jobStats.map((stat) => (
          <Card key={stat.label}>
            <CardContent className="p-4 text-center">
              <stat.icon className={`h-8 w-8 mx-auto mb-2 ${stat.color}`} />
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Job Categories */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Browse by Category</h2>
        <div className="flex flex-wrap gap-2">
          {jobCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? "bg-blue-600 text-white" 
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              {category.name} ({category.count})
            </button>
          ))}
        </div>
      </section>

      {/* Featured Jobs */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            {searchQuery ? `Search Results (${filteredJobs.length})` : 'Featured Jobs'}
          </h2>
          <Button variant="outline" size="sm">
            View All Jobs
          </Button>
        </div>

        <div className="space-y-4">
          {filteredJobs.map((job) => (
            <Card key={job.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-2xl">
                      {job.logo}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-gray-900">{job.title}</h3>
                        {job.featured && (
                          <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">
                            Featured
                          </span>
                        )}
                        {job.remote && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                            Remote
                          </span>
                        )}
                      </div>
                      
                      <p className="text-gray-600 mb-2">{job.company}</p>
                      <p className="text-sm text-gray-600 mb-3">{job.description}</p>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        {job.skills.map((skill) => (
                          <span 
                            key={skill}
                            className="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded font-medium"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {job.location}
                        </div>
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1" />
                          {job.salary}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {job.posted}
                        </div>
                        <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                          {job.type}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleSaveJob(job.id)}
                      className={savedJobs.includes(job.id) ? "bg-blue-50 text-blue-600" : ""}
                    >
                      <Bookmark className={`h-4 w-4 mr-1 ${savedJobs.includes(job.id) ? "fill-current" : ""}`} />
                      {savedJobs.includes(job.id) ? "Saved" : "Save"}
                    </Button>
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Apply
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Job Alerts */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Never Miss Your Dream Job
          </h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Set up job alerts and get notified when new positions matching your criteria are posted
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input placeholder="Enter keywords or job title" className="flex-1" />
            <Button className="bg-blue-600 hover:bg-blue-700">
              Create Alert
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Career Resources */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Career Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Briefcase className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Salary Guide</h3>
              <p className="text-sm text-gray-600">
                Research salary ranges for your role and location
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Company Reviews</h3>
              <p className="text-sm text-gray-600">
                Read employee reviews and company insights
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Career Trends</h3>
              <p className="text-sm text-gray-600">
                Stay updated with the latest industry trends
              </p>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}
