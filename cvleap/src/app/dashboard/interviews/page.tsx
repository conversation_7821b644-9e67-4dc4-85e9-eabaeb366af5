"use client"

import { useState } from "react"
import { MessageSquare, Play, Clock, Star, Users, Briefcase, Code, Brain } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const interviewCategories = [
  {
    id: "behavioral",
    name: "Behavioral",
    icon: Users,
    color: "bg-blue-50 text-blue-600",
    description: "Questions about your experience and soft skills",
    count: 25
  },
  {
    id: "technical",
    name: "Technical", 
    icon: Code,
    color: "bg-green-50 text-green-600",
    description: "Technical skills and problem-solving questions",
    count: 30
  },
  {
    id: "situational",
    name: "Situational",
    icon: Brain,
    color: "bg-purple-50 text-purple-600", 
    description: "Hypothetical scenarios and problem-solving",
    count: 20
  },
  {
    id: "company-specific",
    name: "Company-Specific",
    icon: Briefcase,
    color: "bg-orange-50 text-orange-600",
    description: "Questions tailored to specific companies",
    count: 15
  }
]

const popularQuestions = [
  {
    id: 1,
    question: "Tell me about yourself",
    category: "behavioral",
    difficulty: "Easy",
    timeLimit: "2-3 minutes",
    tips: "Focus on your professional journey, key achievements, and what you're looking for next."
  },
  {
    id: 2,
    question: "What is your greatest weakness?",
    category: "behavioral", 
    difficulty: "Medium",
    timeLimit: "1-2 minutes",
    tips: "Choose a real weakness but show how you're actively working to improve it."
  },
  {
    id: 3,
    question: "Explain a complex technical concept to a non-technical person",
    category: "technical",
    difficulty: "Medium", 
    timeLimit: "3-4 minutes",
    tips: "Use analogies and simple language. Break down complex ideas into digestible parts."
  },
  {
    id: 4,
    question: "How would you handle a disagreement with a team member?",
    category: "situational",
    difficulty: "Medium",
    timeLimit: "2-3 minutes", 
    tips: "Show your conflict resolution skills and ability to find common ground."
  },
  {
    id: 5,
    question: "Why do you want to work at our company?",
    category: "company-specific",
    difficulty: "Easy",
    timeLimit: "2-3 minutes",
    tips: "Research the company thoroughly and connect their values with your career goals."
  }
]

const mockInterviews = [
  {
    id: 1,
    title: "Software Engineer - Google",
    type: "Technical Interview",
    duration: "45 minutes",
    questions: 8,
    difficulty: "Hard"
  },
  {
    id: 2,
    title: "Product Manager - Meta", 
    type: "Behavioral Interview",
    duration: "30 minutes",
    questions: 6,
    difficulty: "Medium"
  },
  {
    id: 3,
    title: "Data Scientist - Netflix",
    type: "Case Study",
    duration: "60 minutes", 
    questions: 4,
    difficulty: "Hard"
  }
]

export default function InterviewsPage() {
  const [selectedCategory, setSelectedCategory] = useState("all")

  const filteredQuestions = selectedCategory === "all" 
    ? popularQuestions 
    : popularQuestions.filter(q => q.category === selectedCategory)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy": return "bg-green-100 text-green-800"
      case "Medium": return "bg-yellow-100 text-yellow-800" 
      case "Hard": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Job Interview Preparation</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Practice with real interview questions, get AI feedback, and boost your confidence 
          for your next job interview
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-2 border-dashed border-blue-200 hover:border-blue-400 transition-colors cursor-pointer">
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Play className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Start Mock Interview</h3>
            <p className="text-sm text-gray-600 mb-4">Practice with AI interviewer</p>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Start Now
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <MessageSquare className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Practice Questions</h3>
            <p className="text-sm text-gray-600 mb-4">Browse common interview questions</p>
            <Button variant="outline">
              Browse Questions
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
              <Star className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Get Feedback</h3>
            <p className="text-sm text-gray-600 mb-4">AI-powered interview analysis</p>
            <Button variant="outline">
              Get Feedback
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Interview Categories */}
      <section>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Question Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {interviewCategories.map((category) => (
            <Card 
              key={category.id}
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => setSelectedCategory(category.id)}
            >
              <CardContent className="p-6 text-center">
                <div className={`w-12 h-12 rounded-lg ${category.color} flex items-center justify-center mx-auto mb-4`}>
                  <category.icon className="h-6 w-6" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{category.name}</h3>
                <p className="text-sm text-gray-600 mb-3">{category.description}</p>
                <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
                  {category.count} questions
                </span>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Popular Questions */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Popular Interview Questions</h2>
          <div className="flex space-x-2">
            <Button 
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
            >
              All
            </Button>
            {interviewCategories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          {filteredQuestions.map((question) => (
            <Card key={question.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-2">{question.question}</h3>
                    <p className="text-sm text-gray-600 mb-4">{question.tips}</p>
                    
                    <div className="flex items-center space-x-4 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.difficulty)}`}>
                        {question.difficulty}
                      </span>
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-4 w-4 mr-1" />
                        {question.timeLimit}
                      </div>
                      <span className="text-gray-600 capitalize">{question.category}</span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 ml-4">
                    <Button size="sm" variant="outline">
                      <Play className="h-4 w-4 mr-1" />
                      Practice
                    </Button>
                    <Button size="sm">
                      View Answer
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Mock Interviews */}
      <section>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Mock Interviews</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {mockInterviews.map((interview) => (
            <Card key={interview.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">{interview.title}</CardTitle>
                <CardDescription>{interview.type}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Duration</span>
                    <span className="font-medium">{interview.duration}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Questions</span>
                    <span className="font-medium">{interview.questions}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Difficulty</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(interview.difficulty)}`}>
                      {interview.difficulty}
                    </span>
                  </div>
                </div>
                <Button className="w-full mt-4">
                  <Play className="h-4 w-4 mr-2" />
                  Start Interview
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Tips Section */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Interview Success Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Before the Interview</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Research the company and role thoroughly</li>
                <li>• Practice common questions out loud</li>
                <li>• Prepare specific examples using STAR method</li>
                <li>• Plan your outfit and route in advance</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">During the Interview</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Maintain good eye contact and posture</li>
                <li>• Listen carefully and ask clarifying questions</li>
                <li>• Use specific examples to support your answers</li>
                <li>• Ask thoughtful questions about the role</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
