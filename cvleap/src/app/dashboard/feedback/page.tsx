"use client"

import { useState } from "react"
import { MessageCircle, Star, ThumbsUp, ThumbsDown, Send, Bot, User, Clock, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"

const feedbackHistory = [
  {
    id: 1,
    type: "ai",
    date: "2025-01-08",
    rating: 4.5,
    feedback: "Your resume shows strong technical skills and relevant experience. Consider adding more quantified achievements to demonstrate your impact. The format is clean and ATS-friendly.",
    suggestions: [
      "Add specific metrics to your achievements (e.g., 'Increased efficiency by 30%')",
      "Include more industry-specific keywords",
      "Consider adding a brief professional summary at the top"
    ],
    status: "completed"
  },
  {
    id: 2,
    type: "human",
    date: "2025-01-05", 
    rating: 4.8,
    feedback: "Excellent technical background! Your experience section is well-structured. I'd recommend highlighting your leadership experience more prominently and adding soft skills.",
    reviewer: "<PERSON>, Senior HR Manager",
    suggestions: [
      "Emphasize leadership and team management experience",
      "Add a skills section with both technical and soft skills",
      "Consider using action verbs to start each bullet point"
    ],
    status: "completed"
  }
]

const aiSuggestions = [
  "Improve your professional summary",
  "Optimize for ATS compatibility", 
  "Enhance your skills section",
  "Add more quantified achievements",
  "Review formatting and structure",
  "Include relevant keywords"
]

export default function FeedbackPage() {
  const [selectedSuggestion, setSelectedSuggestion] = useState("")
  const [customRequest, setCustomRequest] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)

  const handleGetFeedback = (suggestion?: string) => {
    setIsGenerating(true)
    setTimeout(() => {
      setIsGenerating(false)
      alert(`AI feedback generated for: ${suggestion || customRequest} (Demo mode)`)
    }, 2000)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Resume Feedback</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Get personalized feedback from AI and human experts to improve your resume
        </p>
      </div>

      {/* Quick Feedback Options */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Get Instant AI Feedback</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {aiSuggestions.map((suggestion) => (
            <Card 
              key={suggestion}
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleGetFeedback(suggestion)}
            >
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Bot className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">{suggestion}</h3>
                <Button size="sm" variant="outline" disabled={isGenerating}>
                  {isGenerating ? "Generating..." : "Get Feedback"}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Custom Feedback Request */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageCircle className="h-5 w-5 text-blue-600 mr-2" />
            Custom Feedback Request
          </CardTitle>
          <CardDescription>
            Ask specific questions about your resume or request targeted feedback
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Textarea
              placeholder="What specific aspect of your resume would you like feedback on? (e.g., 'How can I better highlight my leadership experience?' or 'Is my resume too long?')"
              value={customRequest}
              onChange={(e) => setCustomRequest(e.target.value)}
              rows={4}
            />
            <Button 
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => handleGetFeedback()}
              disabled={!customRequest.trim() || isGenerating}
            >
              <Send className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating Feedback..." : "Get AI Feedback"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Human Expert Feedback */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="h-8 w-8 text-purple-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Human Expert Review
          </h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Get personalized feedback from experienced HR professionals and industry experts
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-purple-600 hover:bg-purple-700">
              Request Expert Review ($29)
            </Button>
            <Button variant="outline">
              Learn More
            </Button>
          </div>
          <p className="text-sm text-gray-500 mt-4">
            ⚡ 24-48 hour turnaround • 💯 Money-back guarantee
          </p>
        </CardContent>
      </Card>

      {/* Feedback History */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Previous Feedback</h2>
        <div className="space-y-6">
          {feedbackHistory.map((feedback) => (
            <Card key={feedback.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      feedback.type === 'ai' ? 'bg-blue-100' : 'bg-purple-100'
                    }`}>
                      {feedback.type === 'ai' ? (
                        <Bot className={`h-5 w-5 ${feedback.type === 'ai' ? 'text-blue-600' : 'text-purple-600'}`} />
                      ) : (
                        <User className={`h-5 w-5 ${feedback.type === 'ai' ? 'text-blue-600' : 'text-purple-600'}`} />
                      )}
                    </div>
                    <div>
                      <CardTitle className="text-lg">
                        {feedback.type === 'ai' ? 'AI Feedback' : 'Expert Review'}
                      </CardTitle>
                      {feedback.reviewer && (
                        <CardDescription>{feedback.reviewer}</CardDescription>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium ml-1">{feedback.rating}</span>
                    </div>
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700">{feedback.feedback}</p>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Suggestions:</h4>
                    <ul className="space-y-1">
                      {feedback.suggestions.map((suggestion, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start">
                          <span className="text-blue-600 mr-2">•</span>
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {feedback.date}
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        Helpful
                      </Button>
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Feedback Tips */}
      <Card>
        <CardHeader>
          <CardTitle>How to Get Better Feedback</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Be Specific</h4>
              <p className="text-sm text-gray-600">
                Ask targeted questions about specific sections or aspects of your resume
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Provide Context</h4>
              <p className="text-sm text-gray-600">
                Mention the type of roles you're targeting and your career level
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Include Job Descriptions</h4>
              <p className="text-sm text-gray-600">
                Share job postings you're interested in for more targeted advice
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Act on Feedback</h4>
              <p className="text-sm text-gray-600">
                Implement suggestions and request follow-up reviews to track improvement
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
