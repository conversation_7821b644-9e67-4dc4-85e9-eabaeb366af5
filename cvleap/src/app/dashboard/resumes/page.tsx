"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Plus, Edit3, Download, Eye, Trash2, Co<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DEMO_RESUMES } from "@/lib/demo-auth"

export default function ResumesPage() {
  const [resumes, setResumes] = useState(DEMO_RESUMES)

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this resume?')) {
      setResumes(resumes.filter(resume => resume.id !== id))
      alert('Resume deleted successfully! (Demo mode)')
    }
  }

  const handleDuplicate = (resume: any) => {
    const newResume = {
      ...resume,
      id: `${resume.id}-copy`,
      title: `${resume.title} (Copy)`,
      updatedAt: new Date().toISOString().split('T')[0]
    }
    setResumes([newResume, ...resumes])
    alert('Resume duplicated successfully! (Demo mode)')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Resumes</h1>
          <p className="text-gray-600 mt-2">
            Manage and edit your professional resumes
          </p>
        </div>
        <Link href="/dashboard/resumes/new">
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Create New Resume
          </Button>
        </Link>
      </div>

      {/* Resumes Grid */}
      {resumes.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Plus className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No resumes yet</h3>
          <p className="text-gray-600 mb-6">Create your first resume to get started</p>
          <Link href="/dashboard/resumes/new">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Resume
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {resumes.map((resume) => (
            <Card key={resume.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg line-clamp-2">{resume.title}</CardTitle>
                    <CardDescription className="mt-1">{resume.template}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-1 ml-2">
                    <button 
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => handleDuplicate(resume)}
                      title="Duplicate"
                    >
                      <Copy className="h-4 w-4 text-gray-400" />
                    </button>
                    <button 
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => handleDelete(resume.id)}
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4 text-gray-400" />
                    </button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {/* Status and Date */}
                  <div className="flex items-center justify-between text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      resume.status === 'complete' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {resume.status === 'complete' ? 'Complete' : 'Draft'}
                    </span>
                    <span className="text-gray-600">Updated {resume.updatedAt}</span>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{resume.views}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Download className="h-4 w-4" />
                        <span>{resume.downloads}</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Link href={`/dashboard/resumes/${resume.id}/edit`} className="flex-1">
                      <Button size="sm" className="w-full">
                        <Edit3 className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </Link>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="flex-1"
                      onClick={() => alert('PDF download coming soon! (Demo mode)')}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => alert('Preview coming soon! (Demo mode)')}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Tips Section */}
      <Card>
        <CardHeader>
          <CardTitle>Resume Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Keep it Updated</h4>
              <p className="text-sm text-gray-600">
                Regularly update your resume with new skills, experiences, and achievements.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Tailor for Each Job</h4>
              <p className="text-sm text-gray-600">
                Customize your resume for each application to match the job requirements.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Use Action Verbs</h4>
              <p className="text-sm text-gray-600">
                Start bullet points with strong action verbs like "achieved," "managed," or "developed."
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Quantify Results</h4>
              <p className="text-sm text-gray-600">
                Include numbers and percentages to demonstrate your impact and achievements.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
