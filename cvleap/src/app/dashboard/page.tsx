"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  Plus,
  FileText,
  LayoutTemplate,
  TrendingUp,
  Users,
  Star,
  Clock,
  Download,
  Eye,
  Edit3,
  Trash2,
  Upload,
  BarChart3,
  MessageCircle,
  CheckCircle,
  Map,
  MessageSquare,
  Search,
  Chrome,
  Zap,
  Smartphone,
  Gift,
  Globe,
  FileCheck,
  Briefcase
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DemoAuthService, DemoUser, DEMO_RESUMES, DEMO_STATS } from "@/lib/demo-auth"

// Feature sections data
const dataImportFeatures = [
  {
    id: "pdf-import",
    title: "PDF Import",
    description: "Give your old resume a makeover",
    icon: Upload,
    color: "bg-red-50 text-red-600",
    href: "/dashboard/import/pdf"
  }
]

const documentBuilders = [
  {
    id: "resume-builder",
    title: "Resume Builder",
    description: "Create and edit your resumes",
    icon: FileText,
    color: "bg-blue-50 text-blue-600",
    href: "/dashboard/resumes/new"
  },
  {
    id: "cover-letter-builder",
    title: "Cover Letter Builder",
    description: "Create and edit your cover letters",
    icon: FileCheck,
    color: "bg-purple-50 text-purple-600",
    href: "/dashboard/cover-letters/new"
  },
  {
    id: "website-builder",
    title: "Website Builder",
    description: "Turn your resume into a website",
    icon: Globe,
    color: "bg-green-50 text-green-600",
    href: "/dashboard/websites/new"
  }
]

const resumeOptimization = [
  {
    id: "resume-analysis",
    title: "Resume Analysis",
    description: "Check your resume for issues",
    icon: BarChart3,
    color: "bg-green-50 text-green-600",
    href: "/dashboard/analysis"
  },
  {
    id: "resume-examples",
    title: "Resume Examples",
    description: "From real people who got hired",
    icon: FileText,
    color: "bg-blue-50 text-blue-600",
    href: "/dashboard/examples"
  },
  {
    id: "resume-feedback",
    title: "Resume Feedback",
    description: "Improve your resume with AI feedback",
    icon: MessageCircle,
    color: "bg-purple-50 text-purple-600",
    href: "/dashboard/feedback"
  },
  {
    id: "proofreading",
    title: "Proofreading",
    description: "Human proofreaders are here to help",
    icon: CheckCircle,
    color: "bg-orange-50 text-orange-600",
    href: "/dashboard/proofreading"
  }
]

const careerPlanning = [
  {
    id: "career-map",
    title: "Career Map",
    description: "Discover your ideal career path",
    icon: Map,
    color: "bg-blue-50 text-blue-600",
    href: "/dashboard/career-map"
  },
  {
    id: "interview-questions",
    title: "Interview Questions",
    description: "Prepare for your next job interview",
    icon: MessageSquare,
    color: "bg-purple-50 text-purple-600",
    href: "/dashboard/interviews"
  },
  {
    id: "kickresume-jobs",
    title: "Kickresume Jobs",
    description: "Find the best job for your skills",
    icon: Briefcase,
    color: "bg-orange-50 text-orange-600",
    href: "/dashboard/jobs"
  }
]

const otherTools = [
  {
    id: "chrome-extension",
    title: "Chrome Extension",
    description: "Generate cover letters for job posts",
    icon: Chrome,
    color: "bg-purple-50 text-purple-600",
    href: "/dashboard/chrome-extension"
  },
  {
    id: "openai-gpt",
    title: "OpenAI GPT",
    description: "Create resumes within ChatGPT",
    icon: Zap,
    color: "bg-blue-50 text-blue-600",
    href: "/dashboard/ai-tools"
  },
  {
    id: "mobile-app",
    title: "Mobile App",
    description: "Access your resumes on the go",
    icon: Smartphone,
    color: "bg-red-50 text-red-600",
    href: "/dashboard/mobile"
  },
  {
    id: "perks-benefits",
    title: "Perks & Benefits",
    description: "Explore third-party benefits",
    icon: Gift,
    color: "bg-orange-50 text-orange-600",
    href: "/dashboard/perks"
  }
]

export default function DashboardPage() {
  const [user, setUser] = useState<DemoUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const currentUser = DemoAuthService.getCurrentUser()
        setUser(currentUser)
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const FeatureCard = ({ feature, onClick }: { feature: any, onClick?: () => void }) => (
    <Card
      className="hover:shadow-lg transition-all duration-200 cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-lg ${feature.color}`}>
            <feature.icon className="h-6 w-6" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {feature.title}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {feature.description}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-8 max-w-6xl">
      {/* Data Import Section */}
      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Data Import</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {dataImportFeatures.map((feature) => (
            <FeatureCard
              key={feature.id}
              feature={feature}
              onClick={() => alert(`${feature.title} coming soon! (Demo mode)`)}
            />
          ))}
        </div>
      </section>

      {/* Document Builders Section */}
      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Document Builders</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documentBuilders.map((feature) => (
            <FeatureCard
              key={feature.id}
              feature={feature}
              onClick={() => {
                if (feature.id === 'resume-builder') {
                  window.location.href = feature.href
                } else {
                  alert(`${feature.title} coming soon! (Demo mode)`)
                }
              }}
            />
          ))}
        </div>
      </section>

      {/* Resume Optimization Section */}
      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Resume Optimization</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {resumeOptimization.map((feature) => (
            <FeatureCard
              key={feature.id}
              feature={feature}
              onClick={() => alert(`${feature.title} coming soon! (Demo mode)`)}
            />
          ))}
        </div>
      </section>

      {/* Career Planning Section */}
      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Career Planning</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {careerPlanning.map((feature) => (
            <FeatureCard
              key={feature.id}
              feature={feature}
              onClick={() => alert(`${feature.title} coming soon! (Demo mode)`)}
            />
          ))}
        </div>
      </section>

      {/* Others Section */}
      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Others</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {otherTools.map((feature) => (
            <FeatureCard
              key={feature.id}
              feature={feature}
              onClick={() => alert(`${feature.title} coming soon! (Demo mode)`)}
            />
          ))}
        </div>
      </section>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Resumes</p>
                <p className="text-2xl font-bold text-gray-900">{DEMO_STATS.totalResumes}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">{DEMO_STATS.totalViews}</p>
              </div>
              <Eye className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Downloads</p>
                <p className="text-2xl font-bold text-gray-900">{DEMO_STATS.totalDownloads}</p>
              </div>
              <Download className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold text-gray-900">{DEMO_STATS.completionRate}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Resumes */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Recent Resumes</h2>
          <Link href="/dashboard/resumes">
            <Button variant="outline">View All</Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {DEMO_RESUMES.map((resume) => (
            <Card key={resume.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{resume.title}</CardTitle>
                  <div className="flex items-center space-x-1">
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Edit3 className="h-4 w-4 text-gray-400" />
                    </button>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Trash2 className="h-4 w-4 text-gray-400" />
                    </button>
                  </div>
                </div>
                <CardDescription>{resume.template}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Status</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      resume.status === 'complete' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {resume.status === 'complete' ? 'Complete' : 'Draft'}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Last updated</span>
                    <span className="text-gray-900">{resume.updatedAt}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{resume.views}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Download className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{resume.downloads}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" className="flex-1">
                      <Edit3 className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Tips & Resources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Star className="h-5 w-5 text-yellow-500 mr-2" />
            Pro Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Optimize for ATS</h4>
              <p className="text-sm text-gray-600">
                Use our ATS checker to ensure your resume passes through applicant tracking systems.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Use Action Verbs</h4>
              <p className="text-sm text-gray-600">
                Start bullet points with strong action verbs like "achieved," "managed," or "developed."
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Quantify Results</h4>
              <p className="text-sm text-gray-600">
                Include numbers and percentages to demonstrate your impact and achievements.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Keep It Concise</h4>
              <p className="text-sm text-gray-600">
                Aim for 1-2 pages maximum and focus on your most relevant experiences.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Plan Section */}
      <section className="mt-8">
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Current plan: <span className="text-blue-600">FREE</span>
                </h3>
                <p className="text-gray-600">
                  Upgrade to unlock premium features and templates
                </p>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Star className="h-4 w-4 mr-2" />
                Upgrade to Pro
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}
