"use client"

import { useState } from "react"
import { Map, Target, TrendingUp, Users, DollarSign, Clock, ArrowRight, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const careerPaths = [
  {
    id: "software-engineer",
    title: "Software Engineer",
    description: "Build applications and systems",
    currentLevel: "Junior",
    nextLevel: "Mid-Level",
    avgSalary: "$75,000 - $95,000",
    timeToNext: "1-2 years",
    skills: ["JavaScript", "React", "Node.js", "Python"],
    companies: ["Google", "Microsoft", "Amazon", "Meta"],
    growth: "+22%"
  },
  {
    id: "product-manager",
    title: "Product Manager", 
    description: "Lead product strategy and development",
    currentLevel: "Associate PM",
    nextLevel: "Senior PM",
    avgSalary: "$85,000 - $120,000",
    timeToNext: "2-3 years",
    skills: ["Strategy", "Analytics", "Communication", "Leadership"],
    companies: ["Apple", "Netflix", "Uber", "Airbnb"],
    growth: "+19%"
  },
  {
    id: "data-scientist",
    title: "Data Scientist",
    description: "Analyze data to drive business decisions",
    currentLevel: "Junior Data Scientist",
    nextLevel: "Senior Data Scientist", 
    avgSalary: "$80,000 - $110,000",
    timeToNext: "2-3 years",
    skills: ["Python", "SQL", "Machine Learning", "Statistics"],
    companies: ["Tesla", "LinkedIn", "Spotify", "Slack"],
    growth: "+25%"
  }
]

const skillCategories = [
  {
    name: "Technical Skills",
    skills: ["Programming", "Data Analysis", "Cloud Computing", "AI/ML"],
    color: "bg-blue-50 text-blue-600"
  },
  {
    name: "Soft Skills", 
    skills: ["Leadership", "Communication", "Problem Solving", "Teamwork"],
    color: "bg-green-50 text-green-600"
  },
  {
    name: "Industry Knowledge",
    skills: ["Finance", "Healthcare", "E-commerce", "SaaS"],
    color: "bg-purple-50 text-purple-600"
  }
]

export default function CareerMapPage() {
  const [selectedPath, setSelectedPath] = useState(careerPaths[0])

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Career Map</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Discover your ideal career path and get personalized recommendations 
          to advance your professional journey
        </p>
      </div>

      {/* Career Assessment CTA */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Target className="h-8 w-8 text-blue-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Take Our Career Assessment
          </h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Answer a few questions about your skills, interests, and goals to get 
            personalized career recommendations
          </p>
          <Button className="bg-blue-600 hover:bg-blue-700">
            Start Assessment
          </Button>
        </CardContent>
      </Card>

      {/* Popular Career Paths */}
      <section>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Career Paths</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {careerPaths.map((path) => (
            <Card 
              key={path.id} 
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedPath.id === path.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedPath(path)}
            >
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {path.title}
                  <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    {path.growth}
                  </span>
                </CardTitle>
                <CardDescription>{path.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Current Level</span>
                    <span className="font-medium">{path.currentLevel}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Average Salary</span>
                    <span className="font-medium">{path.avgSalary}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Time to Next Level</span>
                    <span className="font-medium">{path.timeToNext}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Selected Career Path Details */}
      <section>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Career Path: {selectedPath.title}
        </h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Career Progression */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 text-blue-600 mr-2" />
                Career Progression
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <div>
                    <h4 className="font-semibold text-blue-900">Current: {selectedPath.currentLevel}</h4>
                    <p className="text-sm text-blue-700">You are here</p>
                  </div>
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-center">
                  <ArrowRight className="h-6 w-6 text-gray-400" />
                </div>
                
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-semibold text-gray-900">Next: {selectedPath.nextLevel}</h4>
                    <p className="text-sm text-gray-600">{selectedPath.timeToNext}</p>
                  </div>
                  <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Required Skills */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="h-5 w-5 text-yellow-600 mr-2" />
                Key Skills
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {selectedPath.skills.map((skill) => (
                  <span 
                    key={skill}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                  >
                    {skill}
                  </span>
                ))}
              </div>
              <Button className="w-full mt-4" variant="outline">
                Assess My Skills
              </Button>
            </CardContent>
          </Card>

          {/* Salary Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="h-5 w-5 text-green-600 mr-2" />
                Salary Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Current Level</span>
                  <span className="font-semibold">{selectedPath.avgSalary}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Growth Rate</span>
                  <span className="font-semibold text-green-600">{selectedPath.growth}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Time to Promotion</span>
                  <span className="font-semibold">{selectedPath.timeToNext}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Companies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 text-purple-600 mr-2" />
                Top Hiring Companies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {selectedPath.companies.map((company) => (
                  <div 
                    key={company}
                    className="p-3 border rounded-lg text-center hover:bg-gray-50 transition-colors"
                  >
                    <span className="font-medium">{company}</span>
                  </div>
                ))}
              </div>
              <Button className="w-full mt-4" variant="outline">
                View All Jobs
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Skill Development */}
      <section>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Skill Development</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {skillCategories.map((category) => (
            <Card key={category.name}>
              <CardHeader>
                <CardTitle className="text-lg">{category.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {category.skills.map((skill) => (
                    <div 
                      key={skill}
                      className={`p-2 rounded-lg ${category.color} text-sm font-medium`}
                    >
                      {skill}
                    </div>
                  ))}
                </div>
                <Button className="w-full mt-4" variant="outline" size="sm">
                  Learn More
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </div>
  )
}
