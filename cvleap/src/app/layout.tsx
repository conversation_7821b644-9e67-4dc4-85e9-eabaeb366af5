import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "CVLeap - Professional Resume Builder",
  description: "Create stunning, professional resumes with CVLeap. Choose from beautiful templates, customize with ease, and export to PDF.",
  keywords: "resume builder, CV creator, professional resume, job application, career tools",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  );
}
