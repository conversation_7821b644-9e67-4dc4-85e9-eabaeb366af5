import Link from "next/link"
import { <PERSON><PERSON><PERSON>t, ArrowLeft, Crown, Star, Download, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const templates = [
  {
    id: "modern-professional",
    name: "Modern Professional",
    description: "Clean, contemporary design perfect for corporate roles",
    category: "Professional",
    isPremium: false,
    rating: 4.8,
    downloads: 12500,
    previewImage: "/templates/modern-professional.jpg",
    colors: ["#2563eb", "#1e40af", "#3b82f6"]
  },
  {
    id: "creative-bold",
    name: "Creative Bold",
    description: "Eye-catching design for creative professionals",
    category: "Creative",
    isPremium: true,
    rating: 4.9,
    downloads: 8900,
    previewImage: "/templates/creative-bold.jpg",
    colors: ["#7c3aed", "#6d28d9", "#8b5cf6"]
  },
  {
    id: "minimal-clean",
    name: "Minimal Clean",
    description: "Simple, elegant layout that focuses on content",
    category: "Simple",
    isPremium: false,
    rating: 4.7,
    downloads: 15200,
    previewImage: "/templates/minimal-clean.jpg",
    colors: ["#374151", "#4b5563", "#6b7280"]
  },
  {
    id: "executive-elite",
    name: "Executive Elite",
    description: "Sophisticated design for senior-level positions",
    category: "Professional",
    isPremium: true,
    rating: 4.9,
    downloads: 6700,
    previewImage: "/templates/executive-elite.jpg",
    colors: ["#1f2937", "#374151", "#4b5563"]
  },
  {
    id: "tech-innovator",
    name: "Tech Innovator",
    description: "Modern tech-focused design with clean lines",
    category: "Modern",
    isPremium: false,
    rating: 4.8,
    downloads: 11300,
    previewImage: "/templates/tech-innovator.jpg",
    colors: ["#059669", "#047857", "#10b981"]
  },
  {
    id: "artistic-flair",
    name: "Artistic Flair",
    description: "Creative template with artistic elements",
    category: "Creative",
    isPremium: true,
    rating: 4.6,
    downloads: 5400,
    previewImage: "/templates/artistic-flair.jpg",
    colors: ["#dc2626", "#b91c1c", "#ef4444"]
  },
  {
    id: "classic-traditional",
    name: "Classic Traditional",
    description: "Timeless design suitable for any industry",
    category: "Simple",
    isPremium: false,
    rating: 4.5,
    downloads: 18900,
    previewImage: "/templates/classic-traditional.jpg",
    colors: ["#1f2937", "#374151", "#4b5563"]
  },
  {
    id: "startup-dynamic",
    name: "Startup Dynamic",
    description: "Energetic design perfect for startup environments",
    category: "Modern",
    isPremium: true,
    rating: 4.7,
    downloads: 7800,
    previewImage: "/templates/startup-dynamic.jpg",
    colors: ["#f59e0b", "#d97706", "#fbbf24"]
  }
]

const categories = ["All", "Professional", "Creative", "Simple", "Modern"]

export default function TemplatesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2">
              <FileText className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-gray-900">CVLeap</span>
            </Link>
            <div className="hidden md:flex items-center space-x-1 text-sm text-gray-600">
              <Link href="/" className="hover:text-gray-900">Home</Link>
              <span>/</span>
              <span>Templates</span>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/auth/login" className="text-gray-600 hover:text-gray-900">
              Sign In
            </Link>
            <Link 
              href="/auth/register" 
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Get Started
            </Link>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Professional Resume Templates
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Choose from our collection of expertly designed templates. 
            All templates are ATS-friendly and customizable to match your style.
          </p>
          
          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {categories.map((category) => (
              <button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  category === "All" 
                    ? "bg-blue-600 text-white" 
                    : "bg-white text-gray-600 hover:bg-gray-50"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {templates.map((template) => (
            <Card key={template.id} className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
              <div className="relative">
                {/* Template Preview */}
                <div className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                  <div className="absolute inset-4 bg-white rounded shadow-sm">
                    {/* Mock resume content */}
                    <div className="p-3 space-y-2">
                      <div className="h-3 bg-gray-800 rounded w-3/4"></div>
                      <div className="h-2 bg-gray-400 rounded w-1/2"></div>
                      <div className="space-y-1 mt-4">
                        <div className="h-1.5 bg-gray-300 rounded w-full"></div>
                        <div className="h-1.5 bg-gray-300 rounded w-5/6"></div>
                        <div className="h-1.5 bg-gray-300 rounded w-4/6"></div>
                      </div>
                      <div className="mt-4 space-y-1">
                        <div className="h-2 bg-gray-600 rounded w-2/3"></div>
                        <div className="h-1 bg-gray-300 rounded w-full"></div>
                        <div className="h-1 bg-gray-300 rounded w-3/4"></div>
                      </div>
                    </div>
                    
                    {/* Color accent */}
                    <div 
                      className="absolute top-0 left-0 w-1 h-full"
                      style={{ backgroundColor: template.colors[0] }}
                    ></div>
                  </div>
                </div>

                {/* Premium Badge */}
                {template.isPremium && (
                  <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                    <Crown className="h-3 w-3 mr-1" />
                    Pro
                  </div>
                )}

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 space-y-2">
                    <Button className="bg-white text-gray-900 hover:bg-gray-100">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                  </div>
                </div>
              </div>

              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="text-sm text-gray-600">{template.rating}</span>
                  </div>
                </div>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <span className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                    {template.category}
                  </span>
                  <div className="flex items-center space-x-1">
                    <Download className="h-3 w-3" />
                    <span>{template.downloads.toLocaleString()}</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
                    Use Template
                  </Button>
                  <Button variant="outline" size="icon">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to create your perfect resume?
          </h2>
          <p className="text-gray-600 mb-8">
            Join thousands of professionals who have landed their dream jobs with CVLeap.
          </p>
          <Link href="/auth/register">
            <Button className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-3">
              Get Started for Free
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}
