import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { AIService, AIGenerationRequest } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabase()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: AIGenerationRequest = await request.json()
    
    // Validate request
    if (!body.type || !body.context) {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 })
    }

    // Check if user has premium access for AI features
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_premium')
      .eq('id', user.id)
      .single()

    // For demo purposes, allow free users limited AI generations
    // In production, implement proper rate limiting and premium checks
    
    // Generate content using AI
    const result = await AIService.generateResumeContent(body)
    
    // Log the AI generation for analytics and billing
    await supabase
      .from('ai_generations')
      .insert({
        user_id: user.id,
        type: body.type,
        input_data: body.context,
        output_data: result,
        tokens_used: result.content.length // Simplified token counting
      })

    return NextResponse.json(result)
  } catch (error) {
    console.error('AI generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate content' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's AI generation history
    const { data: generations, error } = await supabase
      .from('ai_generations')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(10)

    if (error) {
      throw error
    }

    return NextResponse.json({ generations })
  } catch (error) {
    console.error('Error fetching AI generations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch AI generations' },
      { status: 500 }
    )
  }
}
