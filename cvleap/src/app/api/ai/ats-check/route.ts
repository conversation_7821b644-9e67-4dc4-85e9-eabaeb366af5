import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { AIService } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabase()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { resumeId, jobDescription } = await request.json()
    
    // Validate request
    if (!resumeId) {
      return NextResponse.json({ error: 'Resume ID is required' }, { status: 400 })
    }

    // Get resume data
    const { data: resume, error: resumeError } = await supabase
      .from('resumes')
      .select('data')
      .eq('id', resumeId)
      .eq('user_id', user.id)
      .single()

    if (resumeError || !resume) {
      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })
    }

    // Convert resume data to text for analysis
    const resumeText = convertResumeToText(resume.data)
    
    // Analyze ATS compatibility
    const atsAnalysis = await AIService.optimizeForATS(resumeText, jobDescription || '')
    
    // Generate job match analysis if job description provided
    let jobMatch = null
    if (jobDescription) {
      jobMatch = await AIService.generateJobMatchAnalysis(resume.data, jobDescription)
    }

    // Log the analysis
    await supabase
      .from('ai_generations')
      .insert({
        user_id: user.id,
        type: 'ats_check',
        input_data: { resumeId, jobDescription },
        output_data: { atsAnalysis, jobMatch },
        tokens_used: (atsAnalysis.optimizedContent.length + (jobDescription?.length || 0))
      })

    return NextResponse.json({
      atsAnalysis,
      jobMatch
    })
  } catch (error) {
    console.error('ATS check error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze resume' },
      { status: 500 }
    )
  }
}

function convertResumeToText(resumeData: any): string {
  let text = ''
  
  // Personal info
  const personal = resumeData.personalInfo
  text += `${personal.firstName} ${personal.lastName}\n`
  text += `${personal.email} | ${personal.phone} | ${personal.location}\n`
  if (personal.website) text += `${personal.website}\n`
  if (personal.linkedin) text += `${personal.linkedin}\n`
  if (personal.github) text += `${personal.github}\n`
  text += `\nSUMMARY\n${personal.summary}\n\n`
  
  // Work experience
  if (resumeData.workExperience?.length > 0) {
    text += 'WORK EXPERIENCE\n'
    resumeData.workExperience.forEach((exp: any) => {
      text += `${exp.position} at ${exp.company}\n`
      text += `${exp.location} | ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}\n`
      exp.description.forEach((desc: string) => {
        text += `• ${desc}\n`
      })
      text += '\n'
    })
  }
  
  // Education
  if (resumeData.education?.length > 0) {
    text += 'EDUCATION\n'
    resumeData.education.forEach((edu: any) => {
      text += `${edu.degree} in ${edu.field}\n`
      text += `${edu.institution}, ${edu.location}\n`
      text += `${edu.startDate} - ${edu.current ? 'Present' : edu.endDate}\n`
      if (edu.gpa) text += `GPA: ${edu.gpa}\n`
      if (edu.description) text += `${edu.description}\n`
      text += '\n'
    })
  }
  
  // Skills
  if (resumeData.skills?.length > 0) {
    text += 'SKILLS\n'
    const skillsByCategory = resumeData.skills.reduce((acc: any, skill: any) => {
      if (!acc[skill.category]) acc[skill.category] = []
      acc[skill.category].push(skill.name)
      return acc
    }, {})
    
    Object.entries(skillsByCategory).forEach(([category, skills]) => {
      text += `${category}: ${(skills as string[]).join(', ')}\n`
    })
    text += '\n'
  }
  
  // Projects
  if (resumeData.projects?.length > 0) {
    text += 'PROJECTS\n'
    resumeData.projects.forEach((project: any) => {
      text += `${project.name}\n`
      text += `${project.description}\n`
      text += `Technologies: ${project.technologies.join(', ')}\n`
      if (project.url) text += `URL: ${project.url}\n`
      if (project.github) text += `GitHub: ${project.github}\n`
      text += '\n'
    })
  }
  
  // Certificates
  if (resumeData.certificates?.length > 0) {
    text += 'CERTIFICATIONS\n'
    resumeData.certificates.forEach((cert: any) => {
      text += `${cert.name} - ${cert.issuer} (${cert.date})\n`
      if (cert.description) text += `${cert.description}\n`
      text += '\n'
    })
  }
  
  // Languages
  if (resumeData.languages?.length > 0) {
    text += 'LANGUAGES\n'
    resumeData.languages.forEach((lang: any) => {
      text += `${lang.name} - ${lang.proficiency}\n`
    })
    text += '\n'
  }
  
  return text
}
