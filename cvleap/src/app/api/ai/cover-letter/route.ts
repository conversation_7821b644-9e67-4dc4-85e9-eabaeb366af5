import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { AIService } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabase()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { jobTitle, company, jobDescription, resumeId } = await request.json()
    
    // Validate request
    if (!jobTitle || !company || !resumeId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Get resume data
    const { data: resume, error: resumeError } = await supabase
      .from('resumes')
      .select('data')
      .eq('id', resumeId)
      .eq('user_id', user.id)
      .single()

    if (resumeError || !resume) {
      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })
    }

    // Generate cover letter using AI
    const coverLetter = await AIService.generateCoverLetter(
      jobTitle,
      company,
      jobDescription || '',
      resume.data
    )
    
    // Save the generated cover letter
    const { data: savedCoverLetter, error: saveError } = await supabase
      .from('cover_letters')
      .insert({
        user_id: user.id,
        title: `Cover Letter - ${jobTitle} at ${company}`,
        template_id: 'default', // Use default template for AI-generated letters
        data: {
          jobTitle,
          company,
          content: coverLetter,
          generatedAt: new Date().toISOString()
        }
      })
      .select()
      .single()

    if (saveError) {
      throw saveError
    }

    // Log the AI generation
    await supabase
      .from('ai_generations')
      .insert({
        user_id: user.id,
        type: 'cover_letter',
        input_data: { jobTitle, company, jobDescription, resumeId },
        output_data: { content: coverLetter },
        tokens_used: coverLetter.length
      })

    return NextResponse.json({
      coverLetter,
      savedCoverLetter
    })
  } catch (error) {
    console.error('Cover letter generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate cover letter' },
      { status: 500 }
    )
  }
}
