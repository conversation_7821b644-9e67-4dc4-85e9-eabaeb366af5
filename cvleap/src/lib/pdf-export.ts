import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import { ResumeData } from '@/types/resume'

export interface PDFExportOptions {
  format: 'A4' | 'Letter'
  orientation: 'portrait' | 'landscape'
  quality: 'standard' | 'high'
  includeColors: boolean
}

export class PDFExporter {
  static async exportFromHTML(elementId: string, filename: string, options: PDFExportOptions = {
    format: 'A4',
    orientation: 'portrait',
    quality: 'high',
    includeColors: true
  }): Promise<void> {
    try {
      const element = document.getElementById(elementId)
      if (!element) {
        throw new Error('Resume element not found')
      }

      // Configure html2canvas options
      const canvas = await html2canvas(element, {
        scale: options.quality === 'high' ? 2 : 1,
        useCORS: true,
        allowTaint: true,
        backgroundColor: options.includeColors ? null : '#ffffff',
        logging: false,
        width: element.scrollWidth,
        height: element.scrollHeight
      })

      // Calculate PDF dimensions
      const imgWidth = options.format === 'A4' ? 210 : 216 // mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width

      // Create PDF
      const pdf = new jsPDF({
        orientation: options.orientation,
        unit: 'mm',
        format: options.format.toLowerCase() as 'a4' | 'letter'
      })

      const pageHeight = options.format === 'A4' ? 297 : 279 // mm
      let heightLeft = imgHeight
      let position = 0

      // Add first page
      pdf.addImage(
        canvas.toDataURL('image/png'),
        'PNG',
        0,
        position,
        imgWidth,
        imgHeight
      )
      heightLeft -= pageHeight

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(
          canvas.toDataURL('image/png'),
          'PNG',
          0,
          position,
          imgWidth,
          imgHeight
        )
        heightLeft -= pageHeight
      }

      // Save the PDF
      pdf.save(filename)
    } catch (error) {
      console.error('PDF export error:', error)
      throw new Error('Failed to export PDF')
    }
  }

  static async exportFromData(resumeData: ResumeData, templateConfig: any, filename: string): Promise<void> {
    try {
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      // Set up fonts and colors
      pdf.setFont('helvetica')
      const primaryColor = templateConfig.colors?.primary || '#000000'
      const textColor = templateConfig.colors?.text || '#000000'

      let yPosition = 20
      const leftMargin = 20
      const rightMargin = 190
      const lineHeight = 6

      // Helper function to add text with word wrapping
      const addWrappedText = (text: string, x: number, y: number, maxWidth: number, fontSize: number = 10): number => {
        pdf.setFontSize(fontSize)
        const lines = pdf.splitTextToSize(text, maxWidth)
        pdf.text(lines, x, y)
        return y + (lines.length * lineHeight)
      }

      // Header - Personal Information
      const { personalInfo } = resumeData
      pdf.setFontSize(24)
      pdf.setTextColor(primaryColor)
      pdf.text(`${personalInfo.firstName} ${personalInfo.lastName}`, leftMargin, yPosition)
      yPosition += 10

      pdf.setFontSize(10)
      pdf.setTextColor(textColor)
      const contactInfo = [
        personalInfo.email,
        personalInfo.phone,
        personalInfo.location,
        personalInfo.website,
        personalInfo.linkedin
      ].filter(Boolean).join(' | ')
      
      yPosition = addWrappedText(contactInfo, leftMargin, yPosition, rightMargin - leftMargin)
      yPosition += 5

      // Professional Summary
      if (personalInfo.summary) {
        pdf.setFontSize(14)
        pdf.setTextColor(primaryColor)
        pdf.text('PROFESSIONAL SUMMARY', leftMargin, yPosition)
        yPosition += 8

        pdf.setFontSize(10)
        pdf.setTextColor(textColor)
        yPosition = addWrappedText(personalInfo.summary, leftMargin, yPosition, rightMargin - leftMargin)
        yPosition += 8
      }

      // Work Experience
      if (resumeData.workExperience.length > 0) {
        pdf.setFontSize(14)
        pdf.setTextColor(primaryColor)
        pdf.text('WORK EXPERIENCE', leftMargin, yPosition)
        yPosition += 8

        resumeData.workExperience.forEach((exp) => {
          // Check if we need a new page
          if (yPosition > 250) {
            pdf.addPage()
            yPosition = 20
          }

          pdf.setFontSize(12)
          pdf.setTextColor(textColor)
          pdf.text(exp.position, leftMargin, yPosition)
          
          pdf.setFontSize(10)
          pdf.text(exp.company, leftMargin, yPosition + 5)
          
          const dateRange = `${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}`
          pdf.text(dateRange, rightMargin - pdf.getTextWidth(dateRange), yPosition + 5)
          
          if (exp.location) {
            pdf.text(exp.location, rightMargin - pdf.getTextWidth(exp.location), yPosition)
          }
          
          yPosition += 12

          // Job description
          exp.description.forEach((desc) => {
            if (desc.trim()) {
              yPosition = addWrappedText(`• ${desc}`, leftMargin + 5, yPosition, rightMargin - leftMargin - 5, 9)
              yPosition += 2
            }
          })
          yPosition += 5
        })
      }

      // Education
      if (resumeData.education.length > 0) {
        if (yPosition > 220) {
          pdf.addPage()
          yPosition = 20
        }

        pdf.setFontSize(14)
        pdf.setTextColor(primaryColor)
        pdf.text('EDUCATION', leftMargin, yPosition)
        yPosition += 8

        resumeData.education.forEach((edu) => {
          pdf.setFontSize(12)
          pdf.setTextColor(textColor)
          pdf.text(`${edu.degree} in ${edu.field}`, leftMargin, yPosition)
          
          pdf.setFontSize(10)
          pdf.text(edu.institution, leftMargin, yPosition + 5)
          
          const dateRange = `${edu.startDate} - ${edu.current ? 'Present' : edu.endDate}`
          pdf.text(dateRange, rightMargin - pdf.getTextWidth(dateRange), yPosition + 5)
          
          if (edu.location) {
            pdf.text(edu.location, rightMargin - pdf.getTextWidth(edu.location), yPosition)
          }
          
          yPosition += 12

          if (edu.gpa) {
            pdf.text(`GPA: ${edu.gpa}`, leftMargin + 5, yPosition)
            yPosition += 5
          }

          if (edu.description) {
            yPosition = addWrappedText(edu.description, leftMargin + 5, yPosition, rightMargin - leftMargin - 5, 9)
          }
          yPosition += 5
        })
      }

      // Skills
      if (resumeData.skills.length > 0) {
        if (yPosition > 240) {
          pdf.addPage()
          yPosition = 20
        }

        pdf.setFontSize(14)
        pdf.setTextColor(primaryColor)
        pdf.text('SKILLS', leftMargin, yPosition)
        yPosition += 8

        // Group skills by category
        const skillsByCategory = resumeData.skills.reduce((acc, skill) => {
          if (!acc[skill.category]) acc[skill.category] = []
          acc[skill.category].push(skill.name)
          return acc
        }, {} as Record<string, string[]>)

        Object.entries(skillsByCategory).forEach(([category, skills]) => {
          pdf.setFontSize(10)
          pdf.setTextColor(textColor)
          const skillText = `${category}: ${skills.join(', ')}`
          yPosition = addWrappedText(skillText, leftMargin, yPosition, rightMargin - leftMargin)
          yPosition += 3
        })
      }

      // Projects
      if (resumeData.projects.length > 0) {
        if (yPosition > 220) {
          pdf.addPage()
          yPosition = 20
        }

        pdf.setFontSize(14)
        pdf.setTextColor(primaryColor)
        pdf.text('PROJECTS', leftMargin, yPosition)
        yPosition += 8

        resumeData.projects.forEach((project) => {
          pdf.setFontSize(12)
          pdf.setTextColor(textColor)
          pdf.text(project.name, leftMargin, yPosition)
          yPosition += 6

          pdf.setFontSize(9)
          yPosition = addWrappedText(project.description, leftMargin + 5, yPosition, rightMargin - leftMargin - 5)
          
          if (project.technologies.length > 0) {
            yPosition += 2
            yPosition = addWrappedText(`Technologies: ${project.technologies.join(', ')}`, leftMargin + 5, yPosition, rightMargin - leftMargin - 5)
          }
          
          yPosition += 5
        })
      }

      // Save the PDF
      pdf.save(filename)
    } catch (error) {
      console.error('PDF export error:', error)
      throw new Error('Failed to export PDF')
    }
  }

  static async generatePreview(resumeData: ResumeData, templateConfig: any): Promise<string> {
    try {
      // Create a temporary canvas for preview
      const canvas = document.createElement('canvas')
      canvas.width = 595 // A4 width in pixels at 72 DPI
      canvas.height = 842 // A4 height in pixels at 72 DPI
      
      const ctx = canvas.getContext('2d')
      if (!ctx) throw new Error('Canvas context not available')

      // Set background
      ctx.fillStyle = templateConfig.colors?.background || '#ffffff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Add basic content preview
      ctx.fillStyle = templateConfig.colors?.text || '#000000'
      ctx.font = '24px Arial'
      ctx.fillText(`${resumeData.personalInfo.firstName} ${resumeData.personalInfo.lastName}`, 50, 60)

      ctx.font = '12px Arial'
      ctx.fillText(resumeData.personalInfo.email, 50, 90)
      ctx.fillText(resumeData.personalInfo.phone, 50, 110)

      if (resumeData.personalInfo.summary) {
        ctx.font = '16px Arial'
        ctx.fillStyle = templateConfig.colors?.primary || '#000000'
        ctx.fillText('PROFESSIONAL SUMMARY', 50, 150)
        
        ctx.font = '11px Arial'
        ctx.fillStyle = templateConfig.colors?.text || '#000000'
        // Simple text wrapping for preview
        const words = resumeData.personalInfo.summary.split(' ')
        let line = ''
        let y = 170
        words.forEach((word) => {
          const testLine = line + word + ' '
          if (ctx.measureText(testLine).width > 500) {
            ctx.fillText(line, 50, y)
            line = word + ' '
            y += 15
          } else {
            line = testLine
          }
        })
        ctx.fillText(line, 50, y)
      }

      return canvas.toDataURL('image/png')
    } catch (error) {
      console.error('Preview generation error:', error)
      throw new Error('Failed to generate preview')
    }
  }
}
