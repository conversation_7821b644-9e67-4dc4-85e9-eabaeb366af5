import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface AIGenerationRequest {
  type: 'summary' | 'experience' | 'skills' | 'cover_letter' | 'job_match'
  context: {
    jobTitle?: string
    company?: string
    industry?: string
    experience?: string
    skills?: string[]
    jobDescription?: string
    personalInfo?: any
  }
}

export interface AIGenerationResponse {
  content: string
  suggestions?: string[]
  improvements?: string[]
}

export class AIService {
  static async generateResumeContent(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    try {
      const prompt = this.buildPrompt(request)
      
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an expert resume writer and career coach. You help professionals create compelling, ATS-friendly resume content that highlights their achievements and gets them interviews. Always focus on quantifiable results and action verbs."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      })

      const content = completion.choices[0]?.message?.content || ""
      
      return {
        content,
        suggestions: this.generateSuggestions(request.type),
        improvements: this.generateImprovements(content)
      }
    } catch (error) {
      console.error('AI generation error:', error)
      throw new Error('Failed to generate content with AI')
    }
  }

  static async generateCoverLetter(
    jobTitle: string,
    company: string,
    jobDescription: string,
    resumeData: any
  ): Promise<string> {
    try {
      const prompt = `
        Write a professional cover letter for the following position:
        
        Job Title: ${jobTitle}
        Company: ${company}
        Job Description: ${jobDescription}
        
        Candidate Information:
        Name: ${resumeData.personalInfo.firstName} ${resumeData.personalInfo.lastName}
        Experience: ${resumeData.workExperience.map((exp: any) => `${exp.position} at ${exp.company}`).join(', ')}
        Skills: ${resumeData.skills.map((skill: any) => skill.name).join(', ')}
        
        Requirements:
        - Professional tone
        - Highlight relevant experience and skills
        - Show enthusiasm for the role
        - Keep it concise (3-4 paragraphs)
        - Include specific examples of achievements
      `

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an expert cover letter writer. Create compelling, personalized cover letters that get interviews."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.7,
      })

      return completion.choices[0]?.message?.content || ""
    } catch (error) {
      console.error('Cover letter generation error:', error)
      throw new Error('Failed to generate cover letter')
    }
  }

  static async optimizeForATS(content: string, jobDescription: string): Promise<{
    optimizedContent: string
    score: number
    suggestions: string[]
  }> {
    try {
      const prompt = `
        Analyze and optimize the following resume content for ATS (Applicant Tracking System) compatibility:
        
        Resume Content: ${content}
        Job Description: ${jobDescription}
        
        Please provide:
        1. Optimized version of the content with better ATS keywords
        2. ATS compatibility score (0-100)
        3. Specific suggestions for improvement
        
        Focus on:
        - Relevant keywords from job description
        - Action verbs
        - Quantifiable achievements
        - Industry-specific terminology
        - Proper formatting for ATS parsing
      `

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an ATS optimization expert. Help candidates improve their resume content to pass through applicant tracking systems."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1200,
        temperature: 0.5,
      })

      const response = completion.choices[0]?.message?.content || ""
      
      // Parse the response to extract optimized content, score, and suggestions
      // This is a simplified version - in production, you'd want more robust parsing
      const lines = response.split('\n')
      const optimizedContent = lines.slice(0, lines.length / 2).join('\n')
      const score = Math.floor(Math.random() * 30) + 70 // Mock score for demo
      const suggestions = [
        "Add more industry-specific keywords",
        "Include quantifiable achievements",
        "Use stronger action verbs",
        "Optimize for mobile ATS scanning"
      ]

      return {
        optimizedContent,
        score,
        suggestions
      }
    } catch (error) {
      console.error('ATS optimization error:', error)
      throw new Error('Failed to optimize for ATS')
    }
  }

  static async generateJobMatchAnalysis(resumeData: any, jobDescription: string): Promise<{
    matchScore: number
    strengths: string[]
    gaps: string[]
    recommendations: string[]
  }> {
    try {
      const prompt = `
        Analyze how well this resume matches the job description:
        
        Resume Summary:
        - Experience: ${resumeData.workExperience.map((exp: any) => `${exp.position} at ${exp.company}`).join(', ')}
        - Skills: ${resumeData.skills.map((skill: any) => skill.name).join(', ')}
        - Education: ${resumeData.education.map((edu: any) => `${edu.degree} in ${edu.field}`).join(', ')}
        
        Job Description: ${jobDescription}
        
        Provide:
        1. Match score (0-100)
        2. Key strengths that align with the job
        3. Skill/experience gaps
        4. Specific recommendations to improve the match
      `

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a career counselor and job matching expert. Analyze resume-job fit and provide actionable recommendations."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.6,
      })

      // Mock response for demo - in production, parse the AI response
      return {
        matchScore: Math.floor(Math.random() * 30) + 60,
        strengths: [
          "Strong technical skills alignment",
          "Relevant industry experience",
          "Leadership experience matches requirements"
        ],
        gaps: [
          "Missing specific certification mentioned in job",
          "Could highlight more project management experience",
          "Add more quantifiable achievements"
        ],
        recommendations: [
          "Emphasize your project management experience in the summary",
          "Add specific metrics to your achievements",
          "Consider getting the certification mentioned in the job posting",
          "Highlight any relevant side projects or volunteer work"
        ]
      }
    } catch (error) {
      console.error('Job match analysis error:', error)
      throw new Error('Failed to analyze job match')
    }
  }

  private static buildPrompt(request: AIGenerationRequest): string {
    const { type, context } = request

    switch (type) {
      case 'summary':
        return `
          Write a compelling professional summary for a ${context.jobTitle || 'professional'} with the following background:
          - Industry: ${context.industry || 'Technology'}
          - Experience: ${context.experience || 'Several years of experience'}
          - Key Skills: ${context.skills?.join(', ') || 'Various technical and soft skills'}
          
          Requirements:
          - 3-4 sentences
          - Highlight key achievements and skills
          - Use action-oriented language
          - Make it ATS-friendly
          - Show value proposition to employers
        `

      case 'experience':
        return `
          Write compelling bullet points for a ${context.jobTitle} position at ${context.company}. 
          Current description: ${context.experience}
          
          Requirements:
          - Start with strong action verbs
          - Include quantifiable results where possible
          - Highlight achievements, not just responsibilities
          - Make it relevant to the role
          - Keep each bullet point concise but impactful
          - Generate 3-5 bullet points
        `

      case 'skills':
        return `
          Suggest relevant skills for a ${context.jobTitle} in the ${context.industry} industry.
          Current skills: ${context.skills?.join(', ')}
          
          Provide:
          - Technical skills relevant to the role
          - Soft skills that employers value
          - Industry-specific tools and technologies
          - Certifications that would be beneficial
          
          Format as a simple list.
        `

      default:
        return `Generate professional content for ${type} based on the provided context.`
    }
  }

  private static generateSuggestions(type: string): string[] {
    const suggestions = {
      summary: [
        "Include specific years of experience",
        "Mention your biggest achievement",
        "Add industry-specific keywords",
        "Highlight your unique value proposition"
      ],
      experience: [
        "Use the STAR method (Situation, Task, Action, Result)",
        "Include specific metrics and percentages",
        "Focus on achievements, not just duties",
        "Use industry-relevant keywords"
      ],
      skills: [
        "Group skills by category (Technical, Soft, etc.)",
        "Include proficiency levels",
        "Add relevant certifications",
        "Match skills to job requirements"
      ]
    }

    return suggestions[type as keyof typeof suggestions] || []
  }

  private static generateImprovements(content: string): string[] {
    // Simple analysis - in production, use more sophisticated NLP
    const improvements = []
    
    if (!content.includes('%') && !content.includes('$')) {
      improvements.push("Add quantifiable metrics to show impact")
    }
    
    if (content.split(' ').length < 20) {
      improvements.push("Consider adding more specific details")
    }
    
    if (!content.match(/\b(led|managed|developed|created|improved|increased)\b/i)) {
      improvements.push("Use stronger action verbs")
    }
    
    return improvements
  }
}

// Pre-written phrases library for different sections
export const RESUME_PHRASES = {
  actionVerbs: [
    "Achieved", "Administered", "Analyzed", "Built", "Collaborated", "Created",
    "Delivered", "Developed", "Directed", "Enhanced", "Established", "Executed",
    "Generated", "Implemented", "Improved", "Increased", "Led", "Managed",
    "Optimized", "Organized", "Reduced", "Streamlined", "Supervised", "Transformed"
  ],
  
  achievements: [
    "Increased revenue by X%",
    "Reduced costs by $X",
    "Improved efficiency by X%",
    "Led team of X people",
    "Managed budget of $X",
    "Delivered project X% ahead of schedule",
    "Achieved X% customer satisfaction",
    "Reduced processing time by X%"
  ],
  
  skills: {
    technical: [
      "JavaScript", "Python", "React", "Node.js", "AWS", "Docker", "Kubernetes",
      "SQL", "MongoDB", "Git", "CI/CD", "Agile", "Scrum", "REST APIs"
    ],
    soft: [
      "Leadership", "Communication", "Problem Solving", "Team Collaboration",
      "Project Management", "Critical Thinking", "Adaptability", "Time Management"
    ],
    industry: {
      technology: ["Software Development", "Cloud Computing", "DevOps", "Machine Learning"],
      marketing: ["Digital Marketing", "SEO", "Content Strategy", "Analytics"],
      finance: ["Financial Analysis", "Risk Management", "Investment Strategy", "Compliance"]
    }
  }
}
