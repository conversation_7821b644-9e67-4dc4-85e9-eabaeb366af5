// Demo authentication system for development/testing
// This simulates authentication without requiring Supabase setup

export interface DemoUser {
  id: string
  email: string
  firstName: string
  lastName: string
  isPremium: boolean
  createdAt: string
}

export class DemoAuthService {
  private static readonly STORAGE_KEY = 'cvleap_demo_user'
  private static readonly DEMO_USERS: DemoUser[] = [
    {
      id: 'demo-user-1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      isPremium: true,
      createdAt: new Date().toISOString()
    },
    {
      id: 'demo-user-2',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
      isPremium: false,
      createdAt: new Date().toISOString()
    }
  ]

  static async signIn(email: string, password: string): Promise<{ user: DemoUser | null; error: string | null }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Demo credentials
    if (email === '<EMAIL>' && password === 'demo123') {
      const user = this.DEMO_USERS[0]
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(user))
      return { user, error: null }
    }

    if (email === '<EMAIL>' && password === 'demo123') {
      const user = this.DEMO_USERS[1]
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(user))
      return { user, error: null }
    }

    return { user: null, error: 'Invalid email or password. Try <EMAIL> / demo123' }
  }

  static async signUp(email: string, password: string, firstName: string, lastName: string): Promise<{ user: DemoUser | null; error: string | null }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500))

    // Create new demo user
    const newUser: DemoUser = {
      id: `demo-user-${Date.now()}`,
      email,
      firstName,
      lastName,
      isPremium: false,
      createdAt: new Date().toISOString()
    }

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(newUser))
    return { user: newUser, error: null }
  }

  static async signOut(): Promise<void> {
    localStorage.removeItem(this.STORAGE_KEY)
  }

  static getCurrentUser(): DemoUser | null {
    if (typeof window === 'undefined') return null
    
    try {
      const userData = localStorage.getItem(this.STORAGE_KEY)
      return userData ? JSON.parse(userData) : null
    } catch {
      return null
    }
  }

  static isAuthenticated(): boolean {
    return this.getCurrentUser() !== null
  }

  static async signInWithGoogle(): Promise<{ user: DemoUser | null; error: string | null }> {
    // Simulate Google OAuth
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const user: DemoUser = {
      id: `google-user-${Date.now()}`,
      email: '<EMAIL>',
      firstName: 'Google',
      lastName: 'User',
      isPremium: false,
      createdAt: new Date().toISOString()
    }

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(user))
    return { user, error: null }
  }
}

// Demo data for resumes
export const DEMO_RESUMES = [
  {
    id: "demo-resume-1",
    title: "Software Engineer Resume",
    template: "Modern Professional",
    updatedAt: "2025-01-08",
    status: "complete",
    views: 45,
    downloads: 12
  },
  {
    id: "demo-resume-2", 
    title: "Marketing Manager CV",
    template: "Creative Bold",
    updatedAt: "2025-01-07",
    status: "draft",
    views: 23,
    downloads: 5
  },
  {
    id: "demo-resume-3",
    title: "Data Analyst Resume",
    template: "Clean Minimal",
    updatedAt: "2025-01-05",
    status: "complete",
    views: 67,
    downloads: 18
  }
]

export const DEMO_STATS = {
  totalResumes: DEMO_RESUMES.length,
  totalViews: DEMO_RESUMES.reduce((sum, resume) => sum + resume.views, 0),
  totalDownloads: DEMO_RESUMES.reduce((sum, resume) => sum + resume.downloads, 0),
  completionRate: Math.round((DEMO_RESUMES.filter(r => r.status === 'complete').length / DEMO_RESUMES.length) * 100)
}
