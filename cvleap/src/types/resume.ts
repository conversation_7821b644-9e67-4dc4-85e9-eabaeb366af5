export interface PersonalInfo {
  firstName: string
  lastName: string
  email: string
  phone: string
  location: string
  website?: string
  linkedin?: string
  github?: string
  summary: string
}

export interface WorkExperience {
  id: string
  company: string
  position: string
  location: string
  startDate: string
  endDate: string
  current: boolean
  description: string[]
}

export interface Education {
  id: string
  institution: string
  degree: string
  field: string
  location: string
  startDate: string
  endDate: string
  current: boolean
  gpa?: string
  description?: string
}

export interface Skill {
  id: string
  name: string
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert'
  category: string
}

export interface Project {
  id: string
  name: string
  description: string
  technologies: string[]
  url?: string
  github?: string
  startDate: string
  endDate: string
  current: boolean
}

export interface Certificate {
  id: string
  name: string
  issuer: string
  date: string
  url?: string
  description?: string
}

export interface Language {
  id: string
  name: string
  proficiency: 'Basic' | 'Conversational' | 'Fluent' | 'Native'
}

export interface ResumeData {
  id: string
  userId: string
  title: string
  templateId: string
  personalInfo: PersonalInfo
  workExperience: WorkExperience[]
  education: Education[]
  skills: Skill[]
  projects: Project[]
  certificates: Certificate[]
  languages: Language[]
  customSections: CustomSection[]
  createdAt: string
  updatedAt: string
}

export interface CustomSection {
  id: string
  title: string
  items: CustomSectionItem[]
  order: number
}

export interface CustomSectionItem {
  id: string
  title: string
  subtitle?: string
  description?: string
  date?: string
  location?: string
}

export interface ResumeTemplate {
  id: string
  name: string
  description: string
  category: 'professional' | 'creative' | 'simple' | 'modern'
  isPremium: boolean
  previewImage: string
  colors: {
    primary: string
    secondary: string
    accent: string
    text: string
    background: string
  }
  fonts: {
    heading: string
    body: string
  }
  layout: 'single-column' | 'two-column' | 'sidebar'
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  avatarUrl?: string
  isPremium: boolean
  createdAt: string
  updatedAt: string
}
