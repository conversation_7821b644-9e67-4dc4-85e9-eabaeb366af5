"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chrome.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/demo-auth */ \"(app-pages-browser)/./src/lib/demo-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Feature sections data\nconst dataImportFeatures = [\n    {\n        id: \"pdf-import\",\n        title: \"PDF Import\",\n        description: \"Give your old resume a makeover\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/import/pdf\"\n    }\n];\nconst documentBuilders = [\n    {\n        id: \"resume-builder\",\n        title: \"Resume Builder\",\n        description: \"Create and edit your resumes\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/resumes/new\"\n    },\n    {\n        id: \"cover-letter-builder\",\n        title: \"Cover Letter Builder\",\n        description: \"Create and edit your cover letters\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/cover-letters/new\"\n    },\n    {\n        id: \"website-builder\",\n        title: \"Website Builder\",\n        description: \"Turn your resume into a website\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/websites/new\"\n    }\n];\nconst resumeOptimization = [\n    {\n        id: \"resume-analysis\",\n        title: \"Resume Analysis\",\n        description: \"Check your resume for issues\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/analysis\"\n    },\n    {\n        id: \"resume-examples\",\n        title: \"Resume Examples\",\n        description: \"From real people who got hired\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/examples\"\n    },\n    {\n        id: \"resume-feedback\",\n        title: \"Resume Feedback\",\n        description: \"Improve your resume with AI feedback\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/feedback\"\n    },\n    {\n        id: \"proofreading\",\n        title: \"Proofreading\",\n        description: \"Human proofreaders are here to help\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/proofreading\"\n    }\n];\nconst careerPlanning = [\n    {\n        id: \"career-map\",\n        title: \"Career Map\",\n        description: \"Discover your ideal career path\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/career-map\"\n    },\n    {\n        id: \"interview-questions\",\n        title: \"Interview Questions\",\n        description: \"Prepare for your next job interview\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/interviews\"\n    },\n    {\n        id: \"kickresume-jobs\",\n        title: \"Kickresume Jobs\",\n        description: \"Find the best job for your skills\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/jobs\"\n    }\n];\nconst otherTools = [\n    {\n        id: \"chrome-extension\",\n        title: \"Chrome Extension\",\n        description: \"Generate cover letters for job posts\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/chrome-extension\"\n    },\n    {\n        id: \"openai-gpt\",\n        title: \"OpenAI GPT\",\n        description: \"Create resumes within ChatGPT\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/ai-tools\"\n    },\n    {\n        id: \"mobile-app\",\n        title: \"Mobile App\",\n        description: \"Access your resumes on the go\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/mobile\"\n    },\n    {\n        id: \"perks-benefits\",\n        title: \"Perks & Benefits\",\n        description: \"Explore third-party benefits\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/perks\"\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const fetchData = {\n                \"DashboardPage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const currentUser = _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DemoAuthService.getCurrentUser();\n                        setUser(currentUser);\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    const FeatureCard = (param)=>{\n        let { feature, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg \".concat(feature.color),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 200,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Data Import\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: dataImportFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Document Builders\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: documentBuilders.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>{\n                                    if (feature.id === 'resume-builder') {\n                                        window.location.href = feature.href;\n                                    } else {\n                                        alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"));\n                                    }\n                                }\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Resume Optimization\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: resumeOptimization.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Career Planning\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: careerPlanning.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Others\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: otherTools.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Resumes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalResumes\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Views\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalViews\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Downloads\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalDownloads\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Completion Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    stats.completionRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Recent Resumes\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard/resumes\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: \"View All\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: resumes.map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: resume.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: resume.template\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(resume.status === 'complete' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'),\n                                                            children: resume.status === 'complete' ? 'Complete' : 'Draft'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Last updated\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: resume.updatedAt\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.views\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.downloads\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Download\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, resume.id, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this),\n                                \"Pro Tips\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Optimize for ATS\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Use our ATS checker to ensure your resume passes through applicant tracking systems.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Use Action Verbs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: 'Start bullet points with strong action verbs like \"achieved,\" \"managed,\" or \"developed.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Quantify Results\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Include numbers and percentages to demonstrate your impact and achievements.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Keep It Concise\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Aim for 1-2 pages maximum and focus on your most relevant experiences.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: [\n                                                \"Current plan: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-600\",\n                                                    children: \"FREE\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Upgrade to unlock premium features and templates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade to Pro\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});