"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chrome.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/demo-auth */ \"(app-pages-browser)/./src/lib/demo-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Feature sections data\nconst dataImportFeatures = [\n    {\n        id: \"pdf-import\",\n        title: \"PDF Import\",\n        description: \"Give your old resume a makeover\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/import/pdf\"\n    }\n];\nconst documentBuilders = [\n    {\n        id: \"resume-builder\",\n        title: \"Resume Builder\",\n        description: \"Create and edit your resumes\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/resumes/new\"\n    },\n    {\n        id: \"cover-letter-builder\",\n        title: \"Cover Letter Builder\",\n        description: \"Create and edit your cover letters\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/cover-letters/new\"\n    },\n    {\n        id: \"website-builder\",\n        title: \"Website Builder\",\n        description: \"Turn your resume into a website\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/websites/new\"\n    }\n];\nconst resumeOptimization = [\n    {\n        id: \"resume-analysis\",\n        title: \"Resume Analysis\",\n        description: \"Check your resume for issues\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/analysis\"\n    },\n    {\n        id: \"resume-examples\",\n        title: \"Resume Examples\",\n        description: \"From real people who got hired\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/examples\"\n    },\n    {\n        id: \"resume-feedback\",\n        title: \"Resume Feedback\",\n        description: \"Improve your resume with AI feedback\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/feedback\"\n    },\n    {\n        id: \"proofreading\",\n        title: \"Proofreading\",\n        description: \"Human proofreaders are here to help\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/proofreading\"\n    }\n];\nconst careerPlanning = [\n    {\n        id: \"career-map\",\n        title: \"Career Map\",\n        description: \"Discover your ideal career path\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/career-map\"\n    },\n    {\n        id: \"interview-questions\",\n        title: \"Interview Questions\",\n        description: \"Prepare for your next job interview\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/interviews\"\n    },\n    {\n        id: \"kickresume-jobs\",\n        title: \"Kickresume Jobs\",\n        description: \"Find the best job for your skills\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/jobs\"\n    }\n];\nconst otherTools = [\n    {\n        id: \"chrome-extension\",\n        title: \"Chrome Extension\",\n        description: \"Generate cover letters for job posts\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/chrome-extension\"\n    },\n    {\n        id: \"openai-gpt\",\n        title: \"OpenAI GPT\",\n        description: \"Create resumes within ChatGPT\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/ai-tools\"\n    },\n    {\n        id: \"mobile-app\",\n        title: \"Mobile App\",\n        description: \"Access your resumes on the go\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/mobile\"\n    },\n    {\n        id: \"perks-benefits\",\n        title: \"Perks & Benefits\",\n        description: \"Explore third-party benefits\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/perks\"\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const fetchData = {\n                \"DashboardPage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const currentUser = _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DemoAuthService.getCurrentUser();\n                        setUser(currentUser);\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    const FeatureCard = (param)=>{\n        let { feature, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg \".concat(feature.color),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 200,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Data Import\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: dataImportFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Document Builders\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: documentBuilders.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>{\n                                    if (feature.id === 'resume-builder') {\n                                        window.location.href = feature.href;\n                                    } else {\n                                        alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"));\n                                    }\n                                }\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Resume Optimization\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: resumeOptimization.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Career Planning\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: careerPlanning.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Others\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: otherTools.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Resumes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.totalResumes\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Views\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.totalViews\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Downloads\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.totalDownloads\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Completion Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.completionRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Recent Resumes\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard/resumes\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: \"View All\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_RESUMES.map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: resume.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: resume.template\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(resume.status === 'complete' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'),\n                                                            children: resume.status === 'complete' ? 'Complete' : 'Draft'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Last updated\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: resume.updatedAt\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.views\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.downloads\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Download\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, resume.id, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this),\n                                \"Pro Tips\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Optimize for ATS\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Use our ATS checker to ensure your resume passes through applicant tracking systems.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Use Action Verbs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: 'Start bullet points with strong action verbs like \"achieved,\" \"managed,\" or \"developed.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Quantify Results\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Include numbers and percentages to demonstrate your impact and achievements.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Keep It Concise\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Aim for 1-2 pages maximum and focus on your most relevant experiences.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: [\n                                                \"Current plan: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-600\",\n                                                    children: \"FREE\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Upgrade to unlock premium features and templates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade to Pro\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});