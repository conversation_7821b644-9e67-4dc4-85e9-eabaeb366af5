"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chrome.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-template.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,LayoutTemplate,Map,MessageCircle,MessageSquare,Plus,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/demo-auth */ \"(app-pages-browser)/./src/lib/demo-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Feature sections data\nconst dataImportFeatures = [\n    {\n        id: \"pdf-import\",\n        title: \"PDF Import\",\n        description: \"Give your old resume a makeover\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/import/pdf\"\n    }\n];\nconst documentBuilders = [\n    {\n        id: \"resume-builder\",\n        title: \"Resume Builder\",\n        description: \"Create and edit your resumes\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/resumes/new\"\n    },\n    {\n        id: \"cover-letter-builder\",\n        title: \"Cover Letter Builder\",\n        description: \"Create and edit your cover letters\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/cover-letters/new\"\n    },\n    {\n        id: \"website-builder\",\n        title: \"Website Builder\",\n        description: \"Turn your resume into a website\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/websites/new\"\n    }\n];\nconst resumeOptimization = [\n    {\n        id: \"resume-analysis\",\n        title: \"Resume Analysis\",\n        description: \"Check your resume for issues\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/analysis\"\n    },\n    {\n        id: \"resume-examples\",\n        title: \"Resume Examples\",\n        description: \"From real people who got hired\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/examples\"\n    },\n    {\n        id: \"resume-feedback\",\n        title: \"Resume Feedback\",\n        description: \"Improve your resume with AI feedback\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/feedback\"\n    },\n    {\n        id: \"proofreading\",\n        title: \"Proofreading\",\n        description: \"Human proofreaders are here to help\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/proofreading\"\n    }\n];\nconst careerPlanning = [\n    {\n        id: \"career-map\",\n        title: \"Career Map\",\n        description: \"Discover your ideal career path\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/career-map\"\n    },\n    {\n        id: \"interview-questions\",\n        title: \"Interview Questions\",\n        description: \"Prepare for your next job interview\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/interviews\"\n    },\n    {\n        id: \"kickresume-jobs\",\n        title: \"Kickresume Jobs\",\n        description: \"Find the best job for your skills\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/jobs\"\n    }\n];\nconst otherTools = [\n    {\n        id: \"chrome-extension\",\n        title: \"Chrome Extension\",\n        description: \"Generate cover letters for job posts\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/chrome-extension\"\n    },\n    {\n        id: \"openai-gpt\",\n        title: \"OpenAI GPT\",\n        description: \"Create resumes within ChatGPT\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/ai-tools\"\n    },\n    {\n        id: \"mobile-app\",\n        title: \"Mobile App\",\n        description: \"Access your resumes on the go\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/mobile\"\n    },\n    {\n        id: \"perks-benefits\",\n        title: \"Perks & Benefits\",\n        description: \"Explore third-party benefits\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/perks\"\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const fetchData = {\n                \"DashboardPage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const currentUser = _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DemoAuthService.getCurrentUser();\n                        setUser(currentUser);\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    const FeatureCard = (param)=>{\n        let { feature, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg \".concat(feature.color),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 200,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Data Import\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: dataImportFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/dashboard/resumes/new\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"border-2 border-dashed border-blue-200 hover:border-blue-400 transition-colors cursor-pointer\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"flex flex-col items-center justify-center p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"Create New Resume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Start from scratch or use AI to help you\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/templates\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"flex flex-col items-center justify-center p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"Browse Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"8+ professional designs to choose from\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        children: \"View Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"flex flex-col items-center justify-center p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-2\",\n                                    children: \"Import from LinkedIn\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: \"Save time by importing your profile\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>alert('LinkedIn import coming soon!'),\n                                    children: \"Import Now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Resumes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalResumes\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Views\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalViews\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Downloads\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalDownloads\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Completion Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    stats.completionRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Recent Resumes\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard/resumes\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: \"View All\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: resumes.map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: resume.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: resume.template\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(resume.status === 'complete' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'),\n                                                            children: resume.status === 'complete' ? 'Complete' : 'Draft'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Last updated\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: resume.updatedAt\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.views\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.downloads\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Download\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, resume.id, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_LayoutTemplate_Map_MessageCircle_MessageSquare_Plus_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                \"Pro Tips\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Optimize for ATS\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Use our ATS checker to ensure your resume passes through applicant tracking systems.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Use Action Verbs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: 'Start bullet points with strong action verbs like \"achieved,\" \"managed,\" or \"developed.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Quantify Results\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Include numbers and percentages to demonstrate your impact and achievements.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Keep It Concise\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Aim for 1-2 pages maximum and focus on your most relevant experiences.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});