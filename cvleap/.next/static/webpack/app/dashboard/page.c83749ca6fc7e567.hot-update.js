"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chrome.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,CheckCircle,Chrome,Download,Edit3,Eye,FileCheck,FileText,Gift,Globe,Map,MessageCircle,MessageSquare,Smartphone,Star,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/demo-auth */ \"(app-pages-browser)/./src/lib/demo-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Feature sections data\nconst dataImportFeatures = [\n    {\n        id: \"pdf-import\",\n        title: \"PDF Import\",\n        description: \"Give your old resume a makeover\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/import/pdf\"\n    }\n];\nconst documentBuilders = [\n    {\n        id: \"resume-builder\",\n        title: \"Resume Builder\",\n        description: \"Create and edit your resumes\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/resumes/new\"\n    },\n    {\n        id: \"cover-letter-builder\",\n        title: \"Cover Letter Builder\",\n        description: \"Create and edit your cover letters\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/cover-letters/new\"\n    },\n    {\n        id: \"website-builder\",\n        title: \"Website Builder\",\n        description: \"Turn your resume into a website\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/websites/new\"\n    }\n];\nconst resumeOptimization = [\n    {\n        id: \"resume-analysis\",\n        title: \"Resume Analysis\",\n        description: \"Check your resume for issues\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-green-50 text-green-600\",\n        href: \"/dashboard/analysis\"\n    },\n    {\n        id: \"resume-examples\",\n        title: \"Resume Examples\",\n        description: \"From real people who got hired\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/examples\"\n    },\n    {\n        id: \"resume-feedback\",\n        title: \"Resume Feedback\",\n        description: \"Improve your resume with AI feedback\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/feedback\"\n    },\n    {\n        id: \"proofreading\",\n        title: \"Proofreading\",\n        description: \"Human proofreaders are here to help\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/proofreading\"\n    }\n];\nconst careerPlanning = [\n    {\n        id: \"career-map\",\n        title: \"Career Map\",\n        description: \"Discover your ideal career path\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/career-map\"\n    },\n    {\n        id: \"interview-questions\",\n        title: \"Interview Questions\",\n        description: \"Prepare for your next job interview\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/interviews\"\n    },\n    {\n        id: \"kickresume-jobs\",\n        title: \"Kickresume Jobs\",\n        description: \"Find the best job for your skills\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/jobs\"\n    }\n];\nconst otherTools = [\n    {\n        id: \"chrome-extension\",\n        title: \"Chrome Extension\",\n        description: \"Generate cover letters for job posts\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        color: \"bg-purple-50 text-purple-600\",\n        href: \"/dashboard/chrome-extension\"\n    },\n    {\n        id: \"openai-gpt\",\n        title: \"OpenAI GPT\",\n        description: \"Create resumes within ChatGPT\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        color: \"bg-blue-50 text-blue-600\",\n        href: \"/dashboard/ai-tools\"\n    },\n    {\n        id: \"mobile-app\",\n        title: \"Mobile App\",\n        description: \"Access your resumes on the go\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        color: \"bg-red-50 text-red-600\",\n        href: \"/dashboard/mobile\"\n    },\n    {\n        id: \"perks-benefits\",\n        title: \"Perks & Benefits\",\n        description: \"Explore third-party benefits\",\n        icon: _barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        color: \"bg-orange-50 text-orange-600\",\n        href: \"/dashboard/perks\"\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const fetchData = {\n                \"DashboardPage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const currentUser = _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DemoAuthService.getCurrentUser();\n                        setUser(currentUser);\n                    } catch (error) {\n                        console.error(\"Error fetching data:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    const FeatureCard = (param)=>{\n        let { feature, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"hover:shadow-lg transition-all duration-200 cursor-pointer group\",\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg \".concat(feature.color),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n            lineNumber: 200,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Data Import\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: dataImportFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Document Builders\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: documentBuilders.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>{\n                                    if (feature.id === 'resume-builder') {\n                                        window.location.href = feature.href;\n                                    } else {\n                                        alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"));\n                                    }\n                                }\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Resume Optimization\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: resumeOptimization.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Career Planning\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: careerPlanning.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Others\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: otherTools.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                                feature: feature,\n                                onClick: ()=>alert(\"\".concat(feature.title, \" coming soon! (Demo mode)\"))\n                            }, feature.id, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Resumes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.totalResumes\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Views\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.totalViews\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Downloads\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.totalDownloads\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Completion Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    _lib_demo_auth__WEBPACK_IMPORTED_MODULE_5__.DEMO_STATS.completionRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Recent Resumes\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard/resumes\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: \"View All\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: resumes.map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: resume.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: resume.template\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(resume.status === 'complete' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'),\n                                                            children: resume.status === 'complete' ? 'Complete' : 'Draft'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Last updated\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: resume.updatedAt\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.views\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: resume.downloads\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Download\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, resume.id, true, {\n                                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this),\n                                \"Pro Tips\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Optimize for ATS\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Use our ATS checker to ensure your resume passes through applicant tracking systems.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Use Action Verbs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: 'Start bullet points with strong action verbs like \"achieved,\" \"managed,\" or \"developed.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Quantify Results\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Include numbers and percentages to demonstrate your impact and achievements.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Keep It Concise\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Aim for 1-2 pages maximum and focus on your most relevant experiences.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: [\n                                                \"Current plan: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-600\",\n                                                    children: \"FREE\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Upgrade to unlock premium features and templates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_CheckCircle_Chrome_Download_Edit3_Eye_FileCheck_FileText_Gift_Globe_Map_MessageCircle_MessageSquare_Smartphone_Star_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade to Pro\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kc/cvleap/src/app/dashboard/page.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});