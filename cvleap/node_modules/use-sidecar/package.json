{"name": "use-sidecar", "version": "1.1.3", "description": "Sidecar code splitting utils", "module:es2019": "dist/es2019/index.js", "main": "dist/es5/index.js", "module": "dist/es2015/index.js", "types": "dist/es5/index.d.ts", "devDependencies": {"@size-limit/preset-small-lib": "^11.0.2", "size-limit": "^11.0.2", "@theuiteam/lib-builder": "^0.1.4", "@types/enzyme-adapter-react-16": "^1.0.6", "enzyme-adapter-react-16": "^1.15.6", "react": "^16.8.6", "react-dom": "^16.8.6"}, "engines": {"node": ">=10"}, "scripts": {"dev": "lib-builder dev", "test": "jest", "test:ci": "jest --runInBand --coverage", "build": "lib-builder build && yarn size:report", "release": "yarn build && yarn test", "size": "npx size-limit", "size:report": "npx size-limit --json > .size.json", "lint": "lib-builder lint", "format": "lib-builder format", "update": "lib-builder update", "prepublish": "yarn build && yarn changelog", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "changelog:rewrite": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "sideEffects": ["**/medium.js"], "files": ["dist"], "keywords": ["code spliting", "react", "sidecar"], "homepage": "https://github.com/theKashey/use-sidecar", "author": "theK<PERSON>ey <<EMAIL>>", "license": "MIT", "dependencies": {"detect-node-es": "^1.1.0", "tslib": "^2.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/theKashey/use-sidecar"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix", "git add"], "*.{js,css,json,md}": ["prettier --write", "git add"]}, "resolutions": {"@types/react": "^19.0.0"}, "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true}}