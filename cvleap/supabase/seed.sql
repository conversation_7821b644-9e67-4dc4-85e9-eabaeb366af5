-- Insert template data
INSERT INTO public.templates (id, name, description, category, is_premium, preview_image, config) VALUES
(
    'modern-professional',
    'Modern Professional',
    'Clean, contemporary design perfect for corporate roles',
    'professional',
    false,
    '/templates/modern-professional.jpg',
    '{
        "colors": {
            "primary": "#2563eb",
            "secondary": "#1e40af",
            "accent": "#3b82f6",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "Inter",
            "body": "Inter"
        },
        "layout": "two-column",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "summary": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5},
            "projects": {"enabled": true, "order": 6}
        }
    }'
),
(
    'creative-bold',
    'Creative Bold',
    'Eye-catching design for creative professionals',
    'creative',
    true,
    '/templates/creative-bold.jpg',
    '{
        "colors": {
            "primary": "#7c3aed",
            "secondary": "#6d28d9",
            "accent": "#8b5cf6",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "Poppins",
            "body": "Inter"
        },
        "layout": "sidebar",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "summary": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5},
            "projects": {"enabled": true, "order": 6}
        }
    }'
),
(
    'minimal-clean',
    'Minimal Clean',
    'Simple, elegant layout that focuses on content',
    'simple',
    false,
    '/templates/minimal-clean.jpg',
    '{
        "colors": {
            "primary": "#374151",
            "secondary": "#4b5563",
            "accent": "#6b7280",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "Inter",
            "body": "Inter"
        },
        "layout": "single-column",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "summary": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5}
        }
    }'
),
(
    'executive-elite',
    'Executive Elite',
    'Sophisticated design for senior-level positions',
    'professional',
    true,
    '/templates/executive-elite.jpg',
    '{
        "colors": {
            "primary": "#1f2937",
            "secondary": "#374151",
            "accent": "#4b5563",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "Playfair Display",
            "body": "Inter"
        },
        "layout": "two-column",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "summary": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5},
            "achievements": {"enabled": true, "order": 6}
        }
    }'
),
(
    'tech-innovator',
    'Tech Innovator',
    'Modern tech-focused design with clean lines',
    'modern',
    false,
    '/templates/tech-innovator.jpg',
    '{
        "colors": {
            "primary": "#059669",
            "secondary": "#047857",
            "accent": "#10b981",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "JetBrains Mono",
            "body": "Inter"
        },
        "layout": "two-column",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "summary": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5},
            "projects": {"enabled": true, "order": 6},
            "certifications": {"enabled": true, "order": 7}
        }
    }'
),
(
    'artistic-flair',
    'Artistic Flair',
    'Creative template with artistic elements',
    'creative',
    true,
    '/templates/artistic-flair.jpg',
    '{
        "colors": {
            "primary": "#dc2626",
            "secondary": "#b91c1c",
            "accent": "#ef4444",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "Montserrat",
            "body": "Open Sans"
        },
        "layout": "sidebar",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "summary": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5},
            "portfolio": {"enabled": true, "order": 6}
        }
    }'
),
(
    'classic-traditional',
    'Classic Traditional',
    'Timeless design suitable for any industry',
    'simple',
    false,
    '/templates/classic-traditional.jpg',
    '{
        "colors": {
            "primary": "#1f2937",
            "secondary": "#374151",
            "accent": "#4b5563",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "Times New Roman",
            "body": "Times New Roman"
        },
        "layout": "single-column",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "objective": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5}
        }
    }'
),
(
    'startup-dynamic',
    'Startup Dynamic',
    'Energetic design perfect for startup environments',
    'modern',
    true,
    '/templates/startup-dynamic.jpg',
    '{
        "colors": {
            "primary": "#f59e0b",
            "secondary": "#d97706",
            "accent": "#fbbf24",
            "text": "#1f2937",
            "background": "#ffffff"
        },
        "fonts": {
            "heading": "Nunito",
            "body": "Inter"
        },
        "layout": "two-column",
        "sections": {
            "header": {"enabled": true, "order": 1},
            "summary": {"enabled": true, "order": 2},
            "experience": {"enabled": true, "order": 3},
            "education": {"enabled": true, "order": 4},
            "skills": {"enabled": true, "order": 5},
            "projects": {"enabled": true, "order": 6},
            "achievements": {"enabled": true, "order": 7}
        }
    }'
);
